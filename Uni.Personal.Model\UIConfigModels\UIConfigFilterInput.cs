using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Filter input for UIConfig pagination
    /// </summary>
    public class UIConfigFilterInput : FilterInput
    {
        public string? ConfigType { get; set; }
        public string? TableKey { get; set; }
        public string? GridKey { get; set; }
        public string? Key1 { get; set; }
        public string? Key2 { get; set; }
    }
}
