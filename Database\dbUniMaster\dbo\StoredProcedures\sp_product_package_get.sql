CREATE PROCEDURE [dbo].[sp_product_package_get]
    @userId NVARCHAR(50) = NULL,
    @filter NVARCHAR(30) = NULL,
	@product_id NVARCHAR(50) = NULL,

    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

   

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            IF @product_id is null
                BEGIN
                    SELECT *
                    FROM [dbo].fn_config_list_gets('view_product_package_page', 0)
                    ORDER BY [ordinal];
                END
            ELSE
                BEGIN
                    SELECT *
                    FROM [dbo].fn_config_list_gets('view_product_package_detail_page', 0)
                    ORDER BY [ordinal];
                END
           
    END;
    -- Data
    if (@product_id is null)
        BEGIN
            SELECT @Total = COUNT(a.id)
            FROM  product_package a     

            SET @TotalFiltered = @Total;
      
            SELECT a.name,
                   count_prod = (select count(1) from product_package_details t where t.package_id = a.id),
                   a.description,
                   a.id,
                   a.code
	        FROM  product_package a 
            ORDER BY a.created OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;
        END
    ELSE
        BEGIN
            SELECT @Total = COUNT(a.id)
            FROM dbo.product_package_details a left join product_package b on a.package_id = b.id
                                               left join dbo.fn_config_data_gets('price_package_type') c on a.package_price_type = c.objValue2
            WHERE a.prod_id = @product_id      

            SET @TotalFiltered = @Total;
      
            SELECT name = b.name,
                   price = replace(format(a.price,'###,###,###,##0'),',','.') + N' VNĐ' + c.objValue1,
                   a.users_limit,
                   a.description,
                   a.id
	        FROM dbo.product_package_details a left join product_package b on a.package_id = b.id
                                               left join dbo.fn_config_data_gets('price_package_type') c on a.package_price_type = c.objValue2
            WHERE a.prod_id = @product_id
            ORDER BY a.created OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;
        END
    
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

