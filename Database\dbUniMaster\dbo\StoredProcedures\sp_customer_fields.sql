
-- =============================================
-- Author: AnhTT
-- Create date: 
-- Description:	<PERSON> tiết khách hàng
-- Output: 
-- =============================================
CREATE PROCEDURE [dbo].[sp_customer_fields] @userId NVARCHAR(50) = NULL
    , @id UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    DECLARE @table_key VARCHAR(50) = 'customer'
    DECLARE @groupKey VARCHAR(50) = 'mas_customers_group_update'

    --begin
    --1 thong tin chung
    SELECT @id id
        , tableKey = @table_key
        , groupKey = @groupKey

    --2- cac group
    SELECT *
    FROM [dbo].[fn_get_field_group](@groupKey)
    ORDER BY intOrder

    --
    SELECT [a].[id]
        , [a].[code]
        , [a].[cif_no]
        , [a].[cust_type]
        , [a].[full_name]
        , [a].[birth_day]
        , [a].[avatar]
        , [a].[sex]
        , [a].[per_address]
        , [a].[org_name]
        , [a].[establish_date]
        , [a].[tax]
        , [a].[fax]
        , [a].[postion]
        , [a].[phone_1]
        , [a].[phone_2]
        , [a].[email_1]
        , [a].[email_2]
        , [a].[org_address]
        , [a].[provinve_id]
        , [a].[district_id]
        , [a].[national_id]
        , [a].[description]
        , [a].[created]
        , [a].[created_by]
        , [a].[updated]
        , [a].[updated_by]
    INTO #customer
    FROM customer a
    WHERE a.id = @id

    --
    EXEC sp_get_data_fields @id
        , '#customer'
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , @table_key
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

