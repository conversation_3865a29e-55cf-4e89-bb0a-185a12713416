CREATE PROCEDURE [dbo].[sp_order_account_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
	@product_id NVARCHAR(450) = NULL,
	
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(acc.id)
	FROM dbo.order_account acc
	LEFT JOIN dbo.[order] o ON o.id = acc.ord_id
	LEFT JOIN dbo.order_details od ON od.ord_id = o.id
	WHERE ((dbo.ufn_removeMark(acc.login_uni_id) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%') 
	OR (dbo.ufn_removeMark(acc.login_uni_alias) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%') )
		AND (@product_id IS NULL OR @product_id = od.prod_id)
     GROUP BY acc.id    

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_order_account_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data

    SELECT acc.id,acc.login_uni_id,acc.login_uni_pass,acc.login_uni_alias,o.id AS ord_id,acc.created,acc.created_by,acc.updated,acc.updated_by
	FROM dbo.order_account acc
	LEFT JOIN dbo.[order] o ON o.id = acc.ord_id
	LEFT JOIN dbo.order_details od ON od.ord_id = o.id
	WHERE ((dbo.ufn_removeMark(acc.login_uni_id) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%') 
	OR (dbo.ufn_removeMark(acc.login_uni_alias) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%') )
		AND (@product_id IS NULL OR @product_id = od.prod_id)
	GROUP BY acc.id,acc.login_uni_id,acc.login_uni_pass,acc.login_uni_alias,o.id,acc.created,acc.created_by,acc.updated,acc.updated_by
    ORDER BY CASE
                 WHEN acc.updated > acc.created THEN
                     acc.updated
                 ELSE
                     acc.created
             END DESC,
             acc.login_uni_id DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_account_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order_account',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

