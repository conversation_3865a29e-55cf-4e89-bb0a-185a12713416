CREATE TABLE [dbo].[database] (
    [id]                UNIQUE<PERSON>ENTIFIER CONSTRAINT [DF_database_id] DEFAULT (newid()) NOT NULL,
    [instance_id]       UNIQUEIDENTIFIER NOT NULL,
    [name]              NVA<PERSON>HA<PERSON> (250)   NOT NULL,
    [login_user]        NVA<PERSON><PERSON>R (50)    NOT NULL,
    [login_password]    NVA<PERSON>HAR (250)   NOT NULL,
    [note]              NVARCHAR (250)   NOT NULL,
    [current_version]   UNIQUEIDENTIFIER NULL,
    [number_of_tenants] INT              NULL,
    [created]           DATETIME         CONSTRAINT [DF_database_created] DEFAULT (getdate()) NOT NULL,
    [created_by]        <PERSON><PERSON><PERSON><PERSON><PERSON>ENTIFIER NOT NULL,
    [updated]           D<PERSON><PERSON>IME         NULL,
    [updated_by]        UN<PERSON><PERSON><PERSON><PERSON>ENTIFIER NULL,
    CONSTRAINT [PK_database] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_database_database_instance] FOREIG<PERSON> KEY ([instance_id]) REFERENCES [dbo].[database_instance] ([id]),
    CONSTRAINT [FK_database_database_version] FOREIGN KEY ([current_version]) REFERENCES [dbo].[database_version] ([id])
);


GO

