CREATE TABLE [dbo].[customer_products] (
    [id]          UNIQUE<PERSON>ENTIFIER CONSTRAINT [DEFAULT_customer_products_id] DEFAULT (newid()) NOT NULL,
    [customer_id] UNIQUEIDENTIFIER NOT NULL,
    [product_id]  <PERSON>IQUEIDENTIFIER NOT NULL,
    [start_date]  DATET<PERSON>E         NULL,
    [expire_date] DATETIME         NULL,
    [status]      TINYINT          NULL,
    [reference]   UNIQUEIDENTIFIER NULL,
    [database_id] UNIQUEIDENTIFIER NULL,
    [note]        NVARCHAR (4000)  NULL,
    [created]     DATETIME         CONSTRAINT [DEFAULT_customer_products_created] DEFAULT (getdate()) NULL,
    [created_by]  <PERSON><PERSON>Q<PERSON>IDENTIFIER NULL,
    [updated]     DATETIM<PERSON>         NULL,
    [updated_by]  UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_customer_products_1] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_customer_products_customer] FOREIGN KEY ([customer_id]) REFERENCES [dbo].[customer] ([id]),
    CONSTRAINT [FK_customer_products_database] FOREIGN KEY ([database_id]) REFERENCES [dbo].[database] ([id]),
    CONSTRAINT [FK_customer_products_products] FOREIGN KEY ([product_id]) REFERENCES [dbo].[products] ([id])
);


GO

