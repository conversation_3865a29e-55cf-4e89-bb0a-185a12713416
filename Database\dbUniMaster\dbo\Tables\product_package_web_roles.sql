CREATE TABLE [dbo].[product_package_web_roles] (
    [id]            UN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>FIER NOT NULL,
    [package_id]    UNIQUEIDENTIFIER NULL,
    [web_role_id]   UNIQUEIDENTIFIER NULL,
    [web_role_name] NVARCHAR (100)   NULL,
    [description]   <PERSON><PERSON><PERSON><PERSON><PERSON> (250)   NULL,
    [active]        BIT              NULL,
    [created]       DATETIME         NULL,
    [created_by]    UNIQUE<PERSON>ENTIFIER NULL,
    [updated]       DATETIM<PERSON>         NULL,
    [updated_by]    UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_product_package_web_roles] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_product_package_web_roles_product_package] FOREIGN KEY ([package_id]) REFERENCES [dbo].[product_package] ([id])
);


GO

