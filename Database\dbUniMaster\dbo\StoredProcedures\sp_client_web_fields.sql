
-- exec sp_Client_Web_Fields null,'E10C3ADE-EC16-4511-B467-4848241D52C7'
CREATE procedure [dbo].[sp_client_web_fields]
	@UserId nvarchar(450),
	@webId nvarchar(200)

as
	begin try
	    declare @gridWidth int
		declare @tableKey nvarchar(100) = 'client_webs'

		set @gridWidth = isnull(@gridWidth,0)

		if exists(select webId from Client_Webs where webId = @webId)
		begin
		--1--
			select webId as webId
			      ,@tableKey as tableKey
			  from Client_Webs 
			  where webId = @webId
		--2--
			SELECT  objvalue as group_cd
					,objname as group_name 
			FROM [dbo].fn_config_data_gets ('common_group') 
	    --3--
			select [table_name]
				  ,[field_name]
				  ,[view_type]
				  ,[data_type]
				  ,[ordinal]
				  ,[columnLabel]
				  ,[group_cd]
				    ,isnull(case [data_type] 
					  when 'nvarchar' then convert(nvarchar(451), 
						case [field_name] 
							 when 'webKey' then b.<PERSON><PERSON><PERSON>
							  when 'webUrl' then b.WebUrl
							  when 'webName' then b.WebName
							  when 'description' then b.Description
							  when 'iconUrl' then b.IconUrl
							  when 'clientId' then b.clientId
							  when 'clientIdDev' then b.clientIdDev
							  when 'mod_cd' then b.mod_cd							  

						end) 
					  when 'datetime' then convert(nvarchar(10),
						case [field_name] 
							when 'created_dt' then b.created_dt 
						end ,103) 
					  else convert(nvarchar(50),
						case [field_name] 
							when 'status' then b.[Status]
							when 'isTab' then b.isTab
							--when '' then b.scheme_st 
						end) end,[columnDefault])as columnValue
				  ,[columnClass]
				  ,[columnType]
				  ,[columnObject]
				  ,[isSpecial]
				  ,[isRequire]
				  ,[isDisable]
				  ,[IsVisiable]
				  ,isnull(a.columnTooltip,[columnLabel]) as columnTooltip
			  FROM dbo.sys_config_form a
				  ,Client_Webs b  
			  where a.table_name = @tableKey
			  and (isVisiable = 1 or isRequire =1)
			  and b.webId = @webId
			  order by ordinal
		end
		else
		begin
		--4--
			select @webId as webId
			      ,@tableKey as tableKey
		--5--
			SELECT  objvalue as group_cd
					,objname as group_name 
			FROM [dbo].fn_config_data_gets ('common_group') 
		--6--
			select 0
				,[table_name]
				,[field_name]
				,[view_type]
				,[data_type]
				,[ordinal]
				,[columnLabel]
				,[group_cd]
				,[columnDefault] as [columnValue]
				,[columnClass]
				,[columnType]
				,[columnObject]
				,[isSpecial]
				,[isRequire]
				,[isDisable]
				,[IsVisiable]
				,isnull(a.columnTooltip,[columnLabel]) as columnTooltip
			from (select * from sys_config_form 
				where table_name = @tableKey
					and (isVisiable = 1 or isRequire =1)) as a
			order by a.ordinal
		end

		
		----8--
		--  select * from [dbo].fn_config_list_gets ('view_Client_Web_Tab_Page', @gridWidth) 
		--			order by [ordinal]
		  
		--		--begin
		--		select [TabId]
		--			,[WebId]
		--			,[TabCd]
		--			,[TabName]
		--			,[intPos]
		--			,[CreateDt]
		--		from [dbo].[ClientWebTabs]
		--		where WebId = @webId
		--		order by intPos
		--		--end
		
			 
		----9--menu
		--  select * from [dbo].fn_config_list_gets ('view_Client_Web_Menu_Page', @gridWidth) 
		--			order by [ordinal]
		  
		--  --  begin
		--	select  a.[MenuId] as [MenuId]
		--			,a.[WebId]  as [WebId]
		--			,[Title]
		--			,[Path]
		--			,[Icon]
		--			,[Class]
		--			,[Badge]
		--			,[BadgeClass]
		--			,[isExternalLink]
		--			,[isNavHeader]
		--			,case when exists(select 1 from [Client_Web_Menu] where webId = @webId and menuId = a.parentId) then [parentId] else null end as [parentId]
		--			,a.[intPos] as IntPos
		--			,childCount = (select count(*) from [Client_Web_Menu] where parentId = a.menuId)
		--		from [dbo].[Client_Web_Menu] a 
		--			--left join ClientWebTabs b on a.tabId = b.tabId
		--		where a.WebId = @webId
		--		order by a.intPos
		
		--	SELECT b.[actionId]
		--		  ,b.[actionCd]
		--		  ,a.[menuId]
		--		  ,b.[actionName]
		--		  ,b.created_dt
		--	  FROM [Client_Menu_Action] a 
		--		join client_web_action b on a.[actionId] = b.[actionId]
		--	  WHERE b.webId = @webId

		 -- --10--role
		 -- select * from [dbo].fn_config_list_gets ('view_Client_Web_Role_Page', 100) 
			--		order by [ordinal]
		  
			--select a.webRoleId
			--	,a.[WebId]
			--	,a.[RoleCd]
			--	,a.[RoleName]
			--	,a.created_dt
			--	,b.webName
			--	,countUse = (select count(*) from Client_Role_User where webRoleId = a.webRoleId)
			--from [dbo].[Client_Web_Role] a
			--join Client_Webs b on a.webId = b.webId
			--where b.WebId = @webId
			  
		 --11--tree
		 SELECT [menuRoleId]
			  ,a.[menuId]
			  ,a.[webRoleId]
			  ,a.intPos
			  ,c.roleCd as roleCd
			  ,b.title as title
		  FROM [dbo].[Client_Role_Menu] a 
			join Client_Web_Menu b on a.menuId = b.menuId
		    left join Client_Web_Role c on a.webRoleId = c.webRoleId
		 where c.webId = @webId
		 order by b.intPos

		  --select * from [dbo].fn_config_list_gets ('view_Client_Action_Page', 100) 
				--	order by [ordinal]
		 
		  -- select actionId
		  --       ,actionCd
				-- ,actionName
				-- ,webId
				-- ,created_dt
		  --from client_web_action
		  --where webId = @webId

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Web_Fields ' + error_message()
		set @ErrorProc					= error_procedure()
		set @AddlInfo					= ' '

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'WebMenu', 'GET', @SessionID, @AddlInfo
	
	end catch

GO

