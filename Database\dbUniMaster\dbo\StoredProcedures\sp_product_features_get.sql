CREATE PROCEDURE [dbo].[sp_product_features_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(300) = NULL,
	@product_id NVARCHAR(450) = NULL,

    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;
	
    SELECT @Total = COUNT(pf.id)
    FROM dbo.product_features pf
	LEFT JOIN  dbo.products p ON p.id = pf.prod_id
    WHERE (@filter IS NULL OR dbo.ufn_removeMark(pf.feature_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
	AND (@product_id IS NULL OR p.id = @product_id)

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_product_features_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data

    SELECT pf.*,p.prod_name AS prod_name
	FROM dbo.product_features pf
	LEFT JOIN  dbo.products p ON p.id = pf.prod_id
    WHERE (@filter IS NULL OR dbo.ufn_removeMark(pf.feature_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
	AND (@product_id IS NULL OR @product_id = pf.prod_id)
    ORDER BY CASE
                 WHEN pf.updated > pf.created THEN
                     pf.updated
                 ELSE
                     pf.created
             END DESC,
             pf.feature_name DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_features_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_features',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

