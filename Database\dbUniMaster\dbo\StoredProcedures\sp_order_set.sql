CREATE PROCEDURE [dbo].[sp_order_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @cust_id nvarchar(450) = NULL,
	@ord_name NVARCHAR(50) = NULL,
	@ord_no nvarchar(50) = NULL,
	@ord_status BIT = NULL,
	@ord_status_pay bit = NULL,
	@voucher_no nvarchar(50) = NULL
AS
BEGIN TRY

    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF (@id IS NULL)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.[order]
		(
		    id,
		    cust_id,
		    ord_name,
		    ord_no,
		    ord_status,
		    ord_status_pay,
		    voucher_no,
		    created,
		    created_by
		)
		VALUES
		(   @id,      -- id - uniqueidentifier
		    @cust_id,      -- cust_id - uniqueidentifier
		    @ord_name,       -- ord_name - nvarchar(50)
		    @ord_no,       -- ord_no - nvarchar(50)
		    @ord_status,      -- ord_status - bit
		    @ord_status_pay,      -- ord_status_pay - bit
		    @voucher_no,       -- voucher_no - nvarchar(50)
		    GETDATE(), -- created - datetime
		    @userId
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.[order]
		SET cust_id = @cust_id,
			ord_name = @ord_name,
			ord_no = @ord_no,
			ord_status = @ord_status,
			ord_status_pay = @ord_status_pay,
			voucher_no = @voucher_no,
			updated_by = @userId,
			updated = GETDATE()
        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

