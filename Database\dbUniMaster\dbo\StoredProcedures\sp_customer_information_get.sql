
-- Create the stored procedure in the specified schema
CREATE PROCEDURE [dbo].[sp_customer_information_get] @productCode VARCHAR(50)
    , @customerId UNIQUEIDENTIFIER
AS
BEGIN
    BEGIN TRY
        DECLARE @product_id UNIQUEIDENTIFIER = (
                SELECT id
                FROM products
                WHERE prod_code = @productCode
                )

        SELECT c.id
            ,fullname = c.full_name
            ,[databaseserver] = di.[server]
            ,[databasename] = d.name
            ,[databaseuser] = d.login_user
            ,[databasepassword] = d.login_password
            ,c.created
            ,ExpiredDate = cp.expire_date
        FROM customer_products cp
        INNER JOIN customer c ON cp.customer_id = c.id
        LEFT JOIN [database] d ON cp.database_id = d.id
        LEFT JOIN database_instance di ON d.instance_id = di.id
        WHERE cp.product_id = @product_id
        AND cp.customer_id = @customerId

    END TRY

    BEGIN CATCH
        DECLARE @ErrorNum INT
            , @ErrorMsg VARCHAR(200)
            , @ErrorProc VARCHAR(50)
            , @SessionID INT
            , @AddlInfo VARCHAR(MAX);

        SET @ErrorNum = ERROR_NUMBER();
        SET @ErrorMsg = 'sp_customer_set' + ERROR_MESSAGE();
        SET @ErrorProc = ERROR_PROCEDURE();
        SET @AddlInfo = '';

        EXEC utl_errorlog_set @ErrorNum
            , @ErrorMsg
            , @ErrorProc
            , 'customer'
            , 'SET'
            , @SessionID
            , @AddlInfo;
    END CATCH;
END

GO

