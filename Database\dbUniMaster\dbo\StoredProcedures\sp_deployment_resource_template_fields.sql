-- =============================================
-- Author:		AnhTT
-- Create date: 2025-04-21
-- Description:	<PERSON> tiết mẫu nội dung yaml
-- =============================================
CREATE PROCEDURE [dbo].[sp_deployment_resource_template_fields] @userId NVARCHAR(50) = NULL
    , @id UNIQUEIDENTIFIER = NULL
    , @componentId UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    DECLARE @table_key VARCHAR(50) = 'deployment_resource_template'

    --
    SELECT *
    INTO #customers
    FROM customer
    WHERE id = @id

    --begin
    --1 thong tin chung
    SELECT @id id
        , tableKey = @table_key
        , groupKey = 'common_group'

    --2- cac group
    SELECT *
    FROM [dbo].[fn_get_field_group]('common_group')
    ORDER BY intOrder

    --3 tung o trong group
    EXEC sp_get_data_fields @id
        , @table_key
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_resource_template_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'deployment_resource_template'
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

