CREATE PROCEDURE [dbo].[sp_order2_fields]
    @userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    --
    IF @id IS NOT NULL
       AND NOT EXISTS
    (
        SELECT 1
        FROM [dbo].[order]
        WHERE id = @id
    )
        SET @id = NULL;
    --begin
    --1 thong tin chung
	DECLARE @jump INT = 1;
    SELECT @id [id],@jump jump
	FROM [dbo].[order]
        WHERE id = @id
    --2- cac group
    SELECT 1 [group_cd],
           N'Khởi tạo' [group_name];
	SELECT *
    FROM [dbo].fn_config_list_gets('view_orderDetails_curator_page', 0)
    ORDER BY [ordinal];
	SELECT *
    FROM [dbo].fn_config_list_gets('view_contract_page', 0)
    ORDER BY [ordinal];
    --3 tung o trong group
	-- 3.1. Thông tin đơn hàng
    exec sp_get_data_fields @id,'order'
	--3.2. Thông tin tài khoản dùng thử
	SELECT ord_de.id,ord_de.package_id,ord_de.prod_id,ord_de.start_dt,ord_de.end_dt,oc.login_uni_id,oc.login_uni_pass,
	assign = STUFF(
                    (
                        SELECT ',' + full_name
                        FROM dbo.curator c
                        LEFT JOIN dbo.orderDetails_curator od_c ON od_c.curator_id = c.id
						LEFT JOIN dbo.order_details od ON od.id = od_c.ordDetail_id
                        WHERE od.id = ord_de.id
                        FOR XML PATH('')
                    ),
                    1,
                    1,
                    ''
                         )
	FROM dbo.order_details ord_de
	LEFT JOIN dbo.orderDetails_curator odc ON odc.ordDetail_id = ord_de.id
	LEFT JOIN dbo.curator c ON  c.id = odc.curator_id
	LEFT JOIN dbo.order_account oc ON oc.ordDetails_id = ord_de.id
	WHERE ord_de.ord_id = @id
	GROUP BY ord_de.id,ord_de.package_id,ord_de.prod_id,ord_de.start_dt,ord_de.end_dt,oc.login_uni_id,oc.login_uni_pass
	--3.3. Thông tin hợp đồng
	SELECT con.* FROM dbo.contract con
	LEFT JOIN dbo.order_details ord_d ON ord_d.id = con.ord_dts_id
	LEFT JOIN [order] o ON o.id = ord_d.ord_id
	WHERE o.id = @id
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order2_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

