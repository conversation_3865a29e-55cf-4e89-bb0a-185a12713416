CREATE PROCEDURE [dbo].[sp_product_package_menus_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @package_id NVARCHAR(450) = NULL,
    @menu_id NVARCHAR(450) = NULL,
    @description NVARCHAR(250) = NULL,
    @active BIT = 0
AS
BEGIN TRY

    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF (@id IS NULL)
    BEGIN
        SET @id = NEWID();
        -- insert
		INSERT INTO dbo.product_package_menus
		(
		    id,
		    package_id,
		    menu_id,
		    description,
		    active,
		    created,
		    created_by
		)
		VALUES
		(   @id,      -- id - uniqueidentifier
		    @package_id,      -- package_id - uniqueidentifier
		    @menu_id,      -- menu_id - uniqueidentifier
		    @description,       -- description - nvarchar(250)
		    @active,      -- active - bit
		    GETDATE(), -- created - datetime
		    @userId
		    )
        
        --
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
        UPDATE dbo.product_package_menus
        SET package_id = @package_id,
			menu_id = @menu_id,
			description = @description,
			active = @active,
            updated_by = @userId,
            updated = GETDATE()
        WHERE id = @id;

        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_menus_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package_menus',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

