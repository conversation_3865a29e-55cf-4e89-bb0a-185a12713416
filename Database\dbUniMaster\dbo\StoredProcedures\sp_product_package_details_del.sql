CREATE PROCEDURE [dbo].[sp_product_package_details_del]
    @userId NVARCHAR(50),
    @id UNIQUEIDENTIFIER
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF NOT EXISTS (SELECT 1 FROM dbo.product_package_details WHERE id = @id)
    BEGIN
        SET @messages = N'Bản ghi không tồn tại';
        GOTO FINAL;
    END;

    DELETE FROM dbo.product_package_details
    WHERE id = @id;
    --
    SET @valid = 1;
    SET @messages = N'Xóa thành công';
    --
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];
END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_details_del' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package_details',
                          'DEL',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

