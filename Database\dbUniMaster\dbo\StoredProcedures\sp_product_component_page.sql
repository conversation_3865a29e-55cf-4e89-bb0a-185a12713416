CREATE PROCEDURE [dbo].[sp_product_component_page]
    @userId NVARCHAR(450) = NULL,
    @moduleId UNIQUEIDENTIFIER = NULL,
    @filter NVARCHAR(30) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @gridWidth INT = 0,
    @gridKey NVARCHAR(100) = '' OUT,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --
    
    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(1)
    FROM deployment_product_component
          

    SET @TotalFiltered = @Total;

    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_deployment_product_component_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data
	SELECT * FROM deployment_product_component
    ORDER BY created DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_component_page ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'prodeployment_product_componentduct',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH

GO

