

CREATE procedure [dbo].[sp_config_formview_del]
	@UserId		nvarchar(450),
	@id			bigint
as
	begin try	
		begin
			if not exists (select id from sys_config_form where id = @id)
				begin
					delete d from sys_config_form d  where id = @id
				end
	    end

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_CA_PB_Fields_Del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserId	' 

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'ca830pb', 'Del', @SessionID, @AddlInfo
	end catch

GO

