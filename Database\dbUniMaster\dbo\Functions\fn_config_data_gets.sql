




CREATE  FUNCTION [dbo].[fn_config_data_gets]
(
	@fieldObject nvarchar(50)
)
RETURNS @tbl TABLE
(
	 [obj<PERSON><PERSON>] nvarchar(50)
	,[objCode] nvarchar(50)
	,[objN<PERSON>] nvarchar(200)
	,[objGroup] nvarchar(50)
	,[objValue] nvarchar(100)
	,[objValue1] nvarchar(100)
	,[objValue2] int	
	,[intOrder] int	
)
AS
BEGIN
--
	Insert into @tbl 
		SELECT [key_1]
			  ,[key_2]
			  ,[par_desc]
			  ,[key_group]
			  ,case when [type_value] = 1 then cast([value2] as nvarchar(100)) else [value1] end objvalue
			  ,[value1]
			  ,[value2]
			  ,intOrder
	  FROM sys_config_data cc
	where [key_1] = @FieldObject and [isUsed] = 1	
	return 
END

GO

