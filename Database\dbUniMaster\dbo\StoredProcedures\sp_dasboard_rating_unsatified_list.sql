
CREATE PROCEDURE [dbo].[sp_dasboard_rating_unsatified_list]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(150) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @fromDate DATETIME  = '2023-05-01',
    @toDate DATETIME= '2023-05-30',
    @area_Id UNIQUEIDENTIFIER = NULL -- '67DC0318-23CF-4F67-B1A8-D6AE2059EBEB'
AS
BEGIN TRY
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @filter = ISNULL(@filter, '');
	-- Lấy service_id của userId
    DECLARE @service_id UNIQUEIDENTIFIER;
    SELECT @service_id = service_id
    FROM dbo.[user]
    WHERE userId = @userId

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT 1 AS total,
           1 AS totalFiltered,
           valid = 1,
           messages = N'';

    SELECT comment AS rate_dec
    FROM survey_result sr
	LEFT JOIN dbo.survey_question sq ON sq.id = sr.question_id
	LEFT JOIN survey_area a ON a.id = sr.area_id
    WHERE sq.question_type = 1
	AND (@area_Id IS NULL OR @area_Id = a.id)
	AND
              (
                  CAST(sr.created AS DATE) >= CAST(@fromDate AS DATE)
                  And CAST(sr.created AS DATE) <= CAST(@toDate AS DATE)
              )
	AND sr.area_id IS NOT NULL
	AND sq.service_id = @service_id
	ORDER BY a.created desc

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_dasboard_rating_unsatified_list' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' @user: ' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'sp_dasboard_rating_unsatified_list',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

