CREATE TABLE [Admin].[DDL_Log] (
    [DDL_Log_ID]    INT            IDENTITY (1, 1) NOT NULL,
    [EventType]     NVARCHAR (50)  NOT NULL,
    [PostTime]      DATETIME2 (2)  DEFAULT (sysdatetime()) NULL,
    [SPID]          INT            NOT NULL,
    [ServerName]    NVARCHAR (100) NOT NULL,
    [LoginName]     NVARCHAR (100) NOT NULL,
    [OriginalLogin] NVARCHAR (100) NOT NULL,
    [UserName]      NVARCHAR (100) NOT NULL,
    [Application]   NVARCHAR (250) NOT NULL,
    [DatabaseName]  NVARCHAR (100) NOT NULL,
    [SchemaName]    NVARCHAR (100) NOT NULL,
    [ObjectName]    NVARCHAR (100) NOT NULL,
    [ObjectType]    NVARCHAR (100) NOT NULL,
    [TSQLCommand]   NVARCHAR (MAX) NOT NULL,
    [EventData]     XML            NOT NULL,
    CONSTRAINT [PK__DDL_Log__0A1EE7AAE76EC8B8] PRIMARY KEY CLUSTERED ([DDL_Log_ID] ASC)
);


GO

