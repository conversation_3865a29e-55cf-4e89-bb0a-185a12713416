
CREATE PROCEDURE [dbo].[sp_product_feature_list_get] 
@filter NVARCHAR(500) = NULL,
@product_id NVARCHAR(450) = NULL
AS
BEGIN TRY
    SET @filter = ISNULL(@filter, '');
    SELECT CONVERT(NVARCHAR(500), pf.id) AS value,
           feature_name AS name
    FROM dbo.product_features pf
	LEFT JOIN dbo.products p ON p.id = pf.prod_id
    WHERE 
		  ( 
			@filter IS NULL OR dbo.ufn_removeMark(feature_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		  )
		  AND(
			@product_id IS NULL OR @product_id = pf.prod_id
		  )
    ORDER BY name;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_feature_list_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_feature',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

