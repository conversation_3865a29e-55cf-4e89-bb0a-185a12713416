CREATE TABLE [dbo].[payment_transaction] (
    [id]                UNIQ<PERSON>IDENTIFIER NOT NULL,
    [order_id]          UNIQUEIDENTIFIER NOT NULL,
    [amount]            DECIMAL (18, 2)  NOT NULL,
    [currency]          NVARCHAR (3)     CONSTRAINT [DF_payment_transaction_currency] DEFAULT ('VND') NOT NULL,
    [payment_method]    NVARCHAR (50)    NOT NULL,
    [transaction_date]  DATETIME         CONSTRAINT [DF_payment_transaction_transaction_date] DEFAULT (getdate()) NOT NULL,
    [status]            TINYINT          NOT NULL,
    [gateway_reference] NVARCHAR (250)   NULL,
    [gateway_name]      NVARCHAR (250)   NULL,
    [gateway_response]  NVARCHAR (MAX)   NULL,
    [idempotency_key]   UNIQUEIDENTIFIER NULL,
    [last_updated]      DATETIME         CONSTRAINT [DF_payment_transaction_last_updated] DEFAULT (getdate()) NULL,
    CONSTRAINT [PK_payment_transaction] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_payment_transaction_order] FOREIGN KEY ([order_id]) REFERENCES [dbo].[order] ([id])
);


GO

