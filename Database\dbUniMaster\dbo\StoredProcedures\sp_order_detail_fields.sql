CREATE PROCEDURE [dbo].[sp_order_detail_fields]
    @userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    --
    IF @id IS NOT NULL
       AND NOT EXISTS
    (
        SELECT 1
        FROM [dbo].order_details
        WHERE id = @id
    )
        SET @id = NULL;
    --begin
    --1 thong tin chung
    SELECT @id [id];
    --2- cac group
    SELECT 1 [group_cd],
           N'Thông tin chung' [group_name];
    --3 tung o trong group
    exec sp_get_data_fields @id,'order_details'
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_details_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order_details',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

