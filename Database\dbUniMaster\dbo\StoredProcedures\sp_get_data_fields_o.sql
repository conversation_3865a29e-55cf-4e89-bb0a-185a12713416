create PROCEDURE [dbo].[sp_get_data_fields_o] @id nvarchar(50),
    @tableName nvarchar(100) AS BEGIN
IF @id is null or @id = ''
begin
	select 
		[id]
		,[table_name]
		,[field_name]
		,[view_type]
		,[data_type]
		,[ordinal]
		,[group_cd]
		,[columnLabel]
		,[columnTooltip]
		,[columnClass]
		,[columnDisplay]
		,[columnType]
		,[columnObject]
		,[isVisiable]
		,[isSpecial]
		,[isRequire]
		,[isDisable]
		,columnDefault [columnValue]
		  from sys_config_form
		  where table_name = @tableName
			  --and (isVisiable = 1 or isRequire = 1)
		  order by ordinal
end
else
	begin
		DECLARE @sql NVARCHAR(MAX) = N'',
			@tableCols nvarchar(max) = N'',
			@unpivotCols nvarchar(max) = N'' --build columns
		SELECT @tableCols = @tableCols + ',' +case
				when f.data_type =  'date' then 'convert(nvarchar(max),' + QUOTENAME(f.field_name) + ',103) ' + QUOTENAME(f.field_name)
				when f.data_type =  'datetime' then 'format(' + QUOTENAME(f.field_name) + ',''dd/MM/yyyy hh:mm:ss'') ' + QUOTENAME(f.field_name)
				when f.data_type = 'uniqueidentifier' then 'LOWER(cast('+QUOTENAME(f.field_name)+' as nvarchar(max)))' + QUOTENAME(f.field_name)
				when f.data_type = 'bit' or f.columnType = 'checkbox' then 'convert(nvarchar(max),case '+QUOTENAME(f.field_name)+' when 1 then ''True'' else ''False'' end) ' + QUOTENAME(f.field_name)
				else 'convert(nvarchar(max),' + QUOTENAME(f.field_name) + ') ' + QUOTENAME(f.field_name)
			end
		FROM dbo.sys_config_form f
		where table_name = @tableName
		set @tableCols = RIGHT(@tableCols, len(@tableCols) -1) --unpivot cols
		SELECT @unpivotCols = @unpivotCols + ',' + QUOTENAME(f.field_name)
		FROM dbo.sys_config_form f
		where f.table_name = @tableName
		set @unpivotCols = RIGHT(@unpivotCols, len(@unpivotCols) -1) --build sql
		set @sql = 'WITH tempTb as (SELECT * FROM (' + 'SELECT ' + @tableCols + ' FROM ' + QUOTENAME(@tableName) + ' WHERE id=@id' + ')p UNPIVOT( columnValue FOR field_name in (' + @unpivotCols + ')) as unp) ' 
		+ 'SELECt f.[id]
				  ,f.[table_name]
				  ,f.[field_name]
				  ,f.[view_type]
				  ,f.[data_type]
				  ,f.[ordinal]
				  ,f.[group_cd]
				  ,f.[columnLabel]
				  ,f.[columnTooltip]
				  ,f.[columnClass]
				  ,f.[columnDisplay]
				  ,f.[columnType]
				  ,f.[columnObject]
				  ,f.[isVisiable]
				  ,f.[isSpecial]
				  ,f.[isRequire]
				,f.[isDisable]
				,t.columnValue [columnValue]
				FROM sys_config_form f LEFT JOIN tempTb t ON f.field_name = t.field_name WHERE f.table_name = @tableName ORDER BY ordinal' --execute
			exec sp_executesql @sql,
			N'@id nvarchar(50), @tableName nvarchar(100)',
			@id,
			@tableName

	PRINT @sql
	end
END

GO

