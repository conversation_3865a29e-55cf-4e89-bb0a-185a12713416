

CREATE procedure [dbo].[sp_config_formview_set]
	@UserId			nvarchar(450),
	@id				bigint,
	@table_name		nvarchar(100),
	@field_name		nvarchar(100),
	@view_type		int,
	@data_type		nvarchar(100),
	@ordinal		int,
	@group_cd		nvarchar(100),
	@columnLabel	nvarchar(100),
	@columnTooltip	nvarchar(300),
	@columnDefault	nvarchar(300),
	@columnClass	nvarchar(100),
	@columnType		nvarchar(50),
	@columnObject	nvarchar(500),
	@columnValue	nvarchar(max) = null,
	@columnDisplay	nvarchar(100) = null,
	@isVisiable		bit,
	@isSpecial		bit,
	@isRequire		bit,
	@isDisable		bit,
	@isEmpty		bit

as
	begin try	
		if not exists (select id from sys_config_form where table_name = @table_name and field_name = @field_name and view_type = @view_type)
		begin

		INSERT INTO [dbo].sys_config_form
				   ([table_name]
				   ,[field_name]
				   ,[view_type]
				   ,[data_type]
				   ,[ordinal]
				   ,[group_cd]
				   ,[columnLabel]
				   ,[columnTooltip]
				   ,[columnDefault]
				   ,[columnClass]
				   ,[columnType]
				   ,[columnObject]
				   ,[IsVisiable]
				   ,[isSpecial]
				   ,[isRequire]
				   ,[isEmpty]
				   ,[isDisable]
				   ,[columnDisplay]
				   )
			 VALUES
				   (@table_name
				   ,@field_name
				   ,@view_type
				   ,@data_type
				   ,@ordinal
				   ,@group_cd
				   ,@columnLabel
				   ,@columnTooltip
				   ,@columnDefault
				   ,@columnClass
				   ,@columnType
				   ,@columnObject
				   ,@IsVisiable
				   ,@isSpecial
				   ,@isRequire
				   ,@isEmpty
				   ,@isDisable
				   ,@columnDisplay
				   )

			
			set @id = @@IDENTITY
		end
		else
			UPDATE [dbo].sys_config_form
			   SET [table_name] = @table_name
				  ,[field_name] = @field_name
				  ,[view_type] = @view_type
				  ,[data_type] = @data_type
				  ,[ordinal] = @ordinal
				  ,[group_cd] = @group_cd
				  ,[columnLabel] = @columnLabel
				  ,[columnTooltip] = @columnTooltip
				  ,[columnDefault] = @columnDefault
				  ,[columnClass] = @columnClass
				  ,[columnType] = @columnType
				  ,[columnObject] = @columnObject
				  ,[IsVisiable] = @IsVisiable
				  ,[isSpecial] = @isSpecial
				  ,[isRequire] = @isRequire
				  ,[IsEmpty]	= @isEmpty
				  ,[isDisable] = @isDisable
				  ,[columnDisplay] = @columnDisplay
			 WHERE table_name = @table_name and field_name = @field_name and view_type = @view_type

			--select * from ca830pb where table_name = @table_name and field_name = @field_name and view_type = @view_type
			select 1 as valid
				  ,'' as [messages]

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_CA_PB_Fields_Set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserId ' 

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'ca830pb', 'INSERTorUpdate', @SessionID, @AddlInfo
	end catch

GO

