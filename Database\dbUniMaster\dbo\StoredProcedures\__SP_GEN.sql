CREATE PROCEDURE [dbo].[__SP_GEN]
    @table NVARCHAR(100) = 'customer'
AS
BEGIN
DECLARE @table_id INT = OBJECT_ID('dbo.'+@table)
IF OBJECT_ID('tempdb..#temp') IS NOT NULL
    DROP TABLE #temp

SELECT a.[name]
    , a.[max_length]
    , a.[is_nullable]
    , type_name = b.name
    , [data_type] = b.name + CASE 
        WHEN b.name IN ('varchar', 'nvarchar')
            THEN CONCAT (
                    '('
                    , IIF(a.max_length = - 1, 'MAX', CAST(a.max_length AS VARCHAR(5)))
                    , ')'
                    )
        WHEN a.[scale] <> b.[scale]
            THEN CONCAT (
                    '('
                    , a.[precision]
                    , ','
                    , a.[scale]
                    , ')'
                    )
        WHEN a.[precision] <> b.[precision]
            THEN CONCAT (
                    '('
                    , a.[precision]
                    , ')'
                    )
        ELSE ''
        END
    , a.[precision]
    , a.scale
    , a.column_id
INTO #temp
FROM sys.columns a
INNER JOIN sys.types b
    ON a.user_type_id = b.user_type_id
WHERE object_id = @table_id
ORDER BY a.column_id

DECLARE @sp_page VARCHAR(250) = 'sp_' + @table + '_page'
DECLARE @sp_get_fields VARCHAR(250) = 'sp_' + @table + '_fields'
DECLARE @sp_set VARCHAR(250) = 'sp_' + @table + '_set'
DECLARE @sp_delete VARCHAR(250) = 'sp_' + @table + '_del'

DECLARE @columns NVARCHAR(max)
SET @columns =STUFF((SELECT ','+ name FROM #temp FOR XML PATH('')),1,1,'')
-- config page
DECLARE @view_grid NVARCHAR(250) = 'view_' + @table + '_page'

INSERT INTO sys_config_list(
    [view_grid]
    , [view_type]
    , [columnField]
    , [data_type]
    , [columnCaption]
    , [columnWidth]
    , [fieldType]
    , [cellClass]
    , [conditionClass]
    , [pinned]
    , [ordinal]
    , [isUsed]
    , [isHide]
    , [isMasterDetail]
    , [isStatusLable]
    , [isFilter]
    , [sys_dt]
)
SELECT [view_grid] = @view_grid
    , [view_type] = 0
    , [columnField] = name
    , [data_type] = data_type
    , [columnCaption] = name
    , [columnWidth] = 100
    , [fieldType] = type_name
    , [cellClass] = 'border-right'
    , [conditionClass] = NULL
    , [pinned] = NULL
    , [ordinal] = column_id
    , [isUsed] = 1
    , [isHide] = 0
    , [isMasterDetail] = 0
    , [isStatusLable] = 0
    , [isFilter] = 0
    , [sys_dt] = GETDATE()
FROM #temp t--sys_config_list
WHERE NOT EXISTS(SELECT 1 FROM sys_config_list sa WHERE sa.view_grid = @view_grid AND sa.columnField = t.name)
-- config form
INSERT INTO sys_config_form(
    [table_name]
    , [field_name]
    , [view_type]
    , [data_type]
    , [ordinal]
    , [group_cd]
    , [columnLabel]
    , [columnTooltip]
    , [columnDefault]
    , [columnClass]
    , [columnType]
    , [columnObject]
    , [isVisiable]
    , [isSpecial]
    , [isRequire]
    , [isDisable]
    , [IsEmpty]
    , [sys_dt]
    , [columnDisplay]
    , [isIgnore]
)
SELECT [table_name] = @table
    , [field_name] = name
    , [view_type] = 0
    , [data_type] = type_name
    , [ordinal] = column_id
    , [group_cd] = 1
    , [columnLabel] = name
    , [columnTooltip] = NULL
    , [columnDefault] = NULL
    , [columnClass] = 'col-4'
    , [columnType] = CASE WHEN type_name = 'decimal' THEN 'decimal'
                            WHEN type_name = 'datetime' THEN 'datetime'
                            WHEN type_name = 'bit' THEN 'check'
                            WHEN name LIKE '%id' AND name <> 'id' THEN 'dropdown'
                            ELSE 'input'
                    END
    , [columnObject] = NULL
    , [isVisiable] = IIF(name LIKE '%id', 0, 1)
    , [isSpecial] = 0
    , [isRequire] = 0
    , [isDisable] = 0
    , [IsEmpty] = NULL
    , [sys_dt] = GETDATE()
    , [columnDisplay] = NULL
    , [isIgnore] = NULL
FROM #temp t --sys_config_form
WHERE name NOT IN ('created','updated','created_by','updated_by')
AND NOT EXISTS(SELECT 1 FROM sys_config_form sa WHERE sa.table_name = @table AND sa.field_name = t.name)

DELETE a
FROM sys_config_form a
WHERE NOT EXISTS(SELECT TOP 1 1 FROM #temp t WHERE t.name = a.field_name)
AND a.table_name = @table


-- create SP
DECLARE @sp_name NVARCHAR(100) = @sp_page
DECLARE @action NVARCHAR(50) = 'GET'
DECLARE @sql_cmd NVARCHAR(max) =
N'CREATE PROCEDURE {{SP_NAME}}
    {{PARAMS}}
AS
BEGIN TRY
    {{QUERY}}
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = ''{{SP_NAME}}'' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '''';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc, 
                          '''+@table+''',
                          ''{{ACTION}}'',
                          @SessionID,
                          @AddlInfo;
END CATCH;
'

--page
SET @sp_name = @sp_page
DECLARE @params NVARCHAR(max)
DECLARE @query NVARCHAR(max)

SET @params = 
N'@userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
    --more param here!
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT,
    @gridKey NVARCHAR(100) = '''' out
'
SET @query = 
N'SET @gridKey = '''+@view_grid+'''
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '''');

    IF @PageSize <= 0
            SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(1)
        FROM '+@table+'
        --WHERE 

    SET @TotalFiltered = @Total;

    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets(@gridKey, 0)
            ORDER BY [ordinal];
    END;

    --grid data
    SELECT '+@columns+'
    FROM dbo.customer
    --WHERE 
    ORDER BY created  DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
'

DECLARE @sql_page_cmd NVARCHAR(MAX) = REPLACE(REPLACE(REPLACE(REPLACE(@sql_cmd,'{{PARAMS}}',@params),'{{QUERY}}',@query),'{{SP_NAME}}',@sp_name),'{{ACTION}}',@action)

PRINT 'Generate page SP'
--print @sql_page_cmd

PRINT 'Generate fields'
SET @sp_name = @sp_get_fields
SET @params = 
N'@userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = NULL'

SET @query =
N'DECLARE @table_key NVARCHAR(100) = '''+@table+'''
    IF @id IS NOT NULL
       AND NOT EXISTS
    (
        SELECT 1
        FROM '+@table+'
        WHERE id = @id
    )
        SET @id = NULL;
    --1 thong tin chung
    SELECT @id [id];
    --2- cac group
    SELECT @id gd
		   ,tableKey			= @table_key
		   ,groupKey			= ''common_group''
    --3 tung o trong group
    exec sp_get_data_fields @id,'''+@table+'''
;
'

DECLARE @sql_fields_cmd NVARCHAR(MAX) = REPLACE(REPLACE(REPLACE(REPLACE(@sql_cmd,'{{PARAMS}}',@params),'{{QUERY}}',@query),'{{SP_NAME}}',@sp_name),'{{ACTION}}',@action)
--PRINT @sql_fields_cmd


--SET
SET @action = 'SET'
DECLARE @insert_columns NVARCHAR(MAX) = STUFF((SELECT ', '+QUOTENAME(name) FROM #temp WHERE name NOT IN ('created','updated','created_by', 'updated_by') FOR XML PATH('')),1,1,'')
DECLARE @column_values NVARCHAR(MAX) = STUFF((SELECT ', @'+name FROM #temp WHERE name NOT IN ('created','updated','created_by', 'updated_by') FOR XML PATH('')),1,1,'')
DECLARE @update_clumns NVARCHAR(MAX) = STUFF((SELECT ', '+QUOTENAME(name) +' = @'+name FROM #temp WHERE name NOT IN ('created','updated','created_by', 'updated_by') FOR XML PATH('')),1,1,'')
SET @params = '@userId NVARCHAR(50)' + (SELECT ', @'+ name + ' '+ data_type + IIF(name = 'id','', ' = NULL') FROM #temp WHERE name NOT IN ('created','updated','created_by', 'updated_by') FOR XML PATH(''))
SET @query = 
N'DECLARE @valid BIT = 0
        , @messages NVARCHAR(250);

    IF (@id IS NULL OR NOT EXISTS(SELECT 1 FROM '+@table+' WHERE id = @id))
    BEGIN
        IF @id IS NULL
            SET @id = NEWID();

        -- insert
        INSERT INTO '+@table+' (
            '+@insert_columns+'
            , created
            , created_by
            )
        VALUES (
            '+@column_values+'
            , GETDATE()
            , @userId
            )

        --
        SET @valid = 1;
        '+N'SET @messages = N''Thêm mới thành công'';
    END;
    ELSE
    BEGIN
        UPDATE '+@table+'
        SET '+@update_clumns+'
            , updated_by = @userId
            , updated = GETDATE()
        WHERE id = @id;

        --
        SET @valid = 1;
        '+N'SET @messages = N''Cập nhật thành công'';
    END;

    FINAL:

    SELECT @valid valid
        , @messages AS [messages];
'
SET @sp_name = @sp_set
DECLARE @sql_set_cmd NVARCHAR(MAX) = REPLACE(REPLACE(REPLACE(REPLACE(@sql_cmd,'{{PARAMS}}',@params),'{{QUERY}}',@query),'{{SP_NAME}}',@sp_name),'{{ACTION}}',@action)
PRINT @sql_set_cmd

----------------
SET @sp_name = @sp_delete
PRINT @sp_name
--delete
SET @params = 
'@userId NVARCHAR(50)
    ,@id UNIQUEIDENTIFIER'
SET @query =
N'DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF NOT EXISTS (SELECT 1 FROM '+@table+' WHERE id = @id)
    BEGIN
        SET @messages = '+ N'N''Bản ghi không tồn tại'';
        GOTO FINAL;
    END;
    
    DELETE FROM '+@table+'
    WHERE id = @id;
    --
    SET @valid = 1;
    SET @messages = '+ N'N''Xóa thành công'';
    --
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];
'

DECLARE @sql_del_cmd NVARCHAR(MAX) = REPLACE(REPLACE(REPLACE(REPLACE(@sql_cmd,'{{PARAMS}}',@params),'{{QUERY}}',@query),'{{SP_NAME}}',@sp_name),'{{ACTION}}',@action)
PRINT @sql_del_cmd
END

GO

