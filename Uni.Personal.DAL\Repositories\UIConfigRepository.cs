using Uni.Personal.DAL.Interfaces;
using UNI.Common.CommonBase;
using UNI.Model;

namespace Uni.Personal.DAL.Repositories
{
    public class UIConfigRepository : UniBaseRepository, IUIConfigRepository
    {
        public UIConfigRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        public Task<BaseValidate> DelFormViewInfo(long id)
        {
            const string storedProcedure = "sp_bzz_config_formview_del";
            return base.DeleteAsync(storedProcedure, id);
        }

        public Task<BaseValidate> DelGridViewInfo(long gridId)
        {
            const string storedProcedure = "sp_bzz_config_gridview_del";
            return base.DeleteAsync(storedProcedure, gridId);
        }

        public Task<CommonViewInfo> GetConfigLanguageFilter(string userId, string acceptLanguage)
        {
            throw new NotImplementedException();
        }

        public Task<CommonListPage> GetFormViewPage(FilterInpTableKey flt)
        {
            const string storedProcedure = "sp_bzz_config_formview_page";
            return base.GetPageAsync(storedProcedure, flt, new { table_name = flt.tableKey });
        }

        public Task<CommonViewInfo> GetGridViewFilter(string userId, string acceptLanguage)
        {
            return base.GetTableFilter("sp_bzz_common_filter", "language_grid_filter");
        }

        public Task<CommonListPage> GetGridViewPage(FilterInpGridKey filter)
        {
            const string storedProcedure = "sp_bzz_config_gridview_page";
            return GetPageAsync(storedProcedure, filter, objParams: null);
        }

        public Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2)
        {
            const string storedProcedure = "sp_bzz_config_parameter_get";
            return GetFieldsAsync<CommonViewInfo>(storedProcedure, param =>
                {
                    param.Add("@key_1", key_1);
                    param.Add("@key_2", key_2);
                    return param;
                });
        }

        public Task SetFilterInfo(string gridKey, Dictionary<string, string> para)
        {
            throw new NotImplementedException();
        }

        public Task<BaseValidate> SetFormViewInfo(ConfigField configField)
        {
            const string storedProcedure = "sp_bzz_config_formview_set";
            return base.SetAsync(storedProcedure, configField);
        }

        public Task<BaseValidate> SetGridViewInfo(ConfigColumn configColumn)
        {
            const string storedProcedure = "sp_bzz_config_gridview_set";
            return base.SetAsync(storedProcedure, configColumn);
        }

        public Task<BaseValidate> SetGroupInfo(CommonViewInfo info)
        {
            const string storedProcedure = "sp_bzz_config_parameter_set";
            var result = base.SetInfoAsync(storedProcedure, info);
            return result;
        }
    }
}