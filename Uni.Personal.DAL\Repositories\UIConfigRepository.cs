using Uni.Personal.DAL.Interfaces;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    public class UIConfigRepository : UniBaseRepository, IUIConfigRepository
    {
        public UIConfigRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        #region Standard CRUD Methods

        /// <summary>
        /// Get paginated list of UI configurations
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of UI configurations</returns>
        public async Task<CommonListPage> GetPageAsync(UIConfigFilterInput filter)
        {
            const string storedProcedure = "sp_personal_uiconfig_page";
            return await GetPageAsync(storedProcedure, filter, param =>
            {
                param.Add("@ConfigType", filter.ConfigType);
                param.Add("@TableKey", filter.TableKey);
                param.Add("@GridKey", filter.GridKey);
                param.Add("@Key1", filter.Key1);
                param.Add("@Key2", filter.Key2);
                return param;
            });
        }

        /// <summary>
        /// Get UI configuration information by ID
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>UI configuration information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            const string storedProcedure = "sp_personal_uiconfig_GetInfo";
            return await GetFirstOrDefaultAsync<CommonViewOidInfo>(storedProcedure, null, new { Oid = oid });
        }

        /// <summary>
        /// Create or update UI configuration
        /// </summary>
        /// <param name="info">UI configuration information</param>
        /// <returns>Validation result with UI configuration ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            const string storedProcedure = "sp_personal_uiconfig_set";
            return await SetAsync<Guid?>(storedProcedure, info);
        }

        /// <summary>
        /// Delete UI configuration
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            const string storedProcedure = "sp_personal_uiconfig_del";
            return await DeleteAsync(storedProcedure, oid);
        }

        #endregion

        #region Existing Specific Methods

        public Task<BaseValidate> DelFormViewInfo(long id)
        {
            const string storedProcedure = "sp_bzz_config_formview_del";
            return base.DeleteAsync(storedProcedure, id);
        }

        public Task<BaseValidate> DelGridViewInfo(long gridId)
        {
            const string storedProcedure = "sp_bzz_config_gridview_del";
            return base.DeleteAsync(storedProcedure, gridId);
        }

        public Task<CommonViewInfo> GetConfigLanguageFilter(string userId, string acceptLanguage)
        {
            throw new NotImplementedException();
        }

        public Task<CommonListPage> GetFormViewPage(FilterInpTableKey flt)
        {
            const string storedProcedure = "sp_bzz_config_formview_page";
            return base.GetPageAsync(storedProcedure, flt, new { table_name = flt.tableKey });
        }

        public Task<CommonViewInfo> GetGridViewFilter(string userId, string acceptLanguage)
        {
            return base.GetTableFilter("sp_bzz_common_filter", "language_grid_filter");
        }

        public Task<CommonListPage> GetGridViewPage(FilterInpGridKey filter)
        {
            const string storedProcedure = "sp_bzz_config_gridview_page";
            return GetPageAsync(storedProcedure, filter, objParams: null);
        }

        public Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2)
        {
            const string storedProcedure = "sp_bzz_config_parameter_get";
            return GetFieldsAsync<CommonViewInfo>(storedProcedure, param =>
                {
                    param.Add("@key_1", key_1);
                    param.Add("@key_2", key_2);
                    return param;
                });
        }

        public Task SetFilterInfo(string gridKey, Dictionary<string, string> para)
        {
            throw new NotImplementedException();
        }

        public Task<BaseValidate> SetFormViewInfo(ConfigField configField)
        {
            const string storedProcedure = "sp_bzz_config_formview_set";
            return base.SetAsync(storedProcedure, configField);
        }

        public Task<BaseValidate> SetGridViewInfo(ConfigColumn configColumn)
        {
            const string storedProcedure = "sp_bzz_config_gridview_set";
            return base.SetAsync(storedProcedure, configColumn);
        }

        public Task<BaseValidate> SetGroupInfo(CommonViewInfo info)
        {
            const string storedProcedure = "sp_bzz_config_parameter_set";
            var result = base.SetInfoAsync(storedProcedure, info);
            return result;
        }

        #endregion
    }
}