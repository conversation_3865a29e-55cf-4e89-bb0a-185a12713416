
CREATE PROCEDURE [dbo].[sp_product_module_fields] @userId NVARCHAR(50) = NULL
    , @id UNIQUEIDENTIFIER = NULL
    , @productId UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    DECLARE @table_key VARCHAR(50) = 'product_module'

    --begin
    --1 thong tin chung
    SELECT @id id
        , tableKey = @table_key
        , groupKey = 'common_group'

    --2- cac group
    SELECT *
    FROM [dbo].[fn_get_field_group]('common_group')
    ORDER BY intOrder

    --
    SELECT a.[id]
        , a.[code]
        , a.[name]
        , [product_id]
        , product_name = p.prod_name
    INTO #product_module
    FROM product_module a
    INNER JOIN products p
        ON p.id = a.product_id
    WHERE a.id = @id

    --
    EXEC sp_get_data_fields @id
        , '#product_module'
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_module_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'product_module'
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

