CREATE PROCEDURE [dbo].[sp_product_line_page]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(id)
    FROM dbo.product_line
    WHERE @filter = '' or prod_line_name LIKE N'%' + dbo.ufn_removeMark(@filter) + '%';
          

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_product_line_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data

     SELECT pl.id
           ,pl.prod_line_name
           ,pl.prod_line_desc
           ,pl.parent_id
           ,pl1.prod_line_name AS parent_prod_line_name
           ,pl.int_ord
           ,pl.level
		   ,pl.prod_line_cd
           ,count_product	= isnull((select count(1) from products t where t.prod_line_id = pl.id),0)
		   ,count_category	= isnull((select count(1) from product_category t where t.prod_line_id = pl.id),0)
		   ,created			= isnull(pl.created,pl.updated)
	FROM dbo.product_line pl
	LEFT JOIN dbo.product_line pl1 ON pl1.id =pl.parent_id
	WHERE @filter = '' or pl.prod_line_name LIKE N'%' + @filter + '%' 
    ORDER BY isnull(pl.updated,pl.created)
             ,pl.prod_line_name DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_line_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_line',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

