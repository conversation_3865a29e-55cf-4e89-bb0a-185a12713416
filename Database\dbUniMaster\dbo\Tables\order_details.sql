CREATE TABLE [dbo].[order_details] (
    [id]         UNIQ<PERSON><PERSON>ENTIFIER CONSTRAINT [DF_order_details_id] DEFAULT (newid()) NOT NULL,
    [ord_id]     UNIQUEIDENTIFIER NULL,
    [prod_id]    UN<PERSON>Q<PERSON><PERSON>ENTIFIER NULL,
    [package_id] UNIQ<PERSON><PERSON>ENTIFIER NULL,
    [start_dt]   DATETIME         NULL,
    [end_dt]     DATETIME         NULL,
    [type]       INT              NULL,
    [amount]     DECIMAL (18)     NULL,
    [created]    DATETIME         NULL,
    [created_by] UNIQUEIDENTIFIER NULL,
    [updated]    D<PERSON>ETIM<PERSON>         NULL,
    [updated_by] UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_mas_order_details] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_order_details_order] FOREIGN KEY ([ord_id]) REFERENCES [dbo].[order] ([id]),
    CONSTRAINT [FK_order_details_products] FOREIGN KEY ([prod_id]) REFERENCES [dbo].[products] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'type: Hình thức dăng ký [vd: đăng ký mới, gia hạn,...]', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', @level2type = N'COLUMN', @level2name = N'type';


GO

