CREATE PROCEDURE [dbo].[sp_order_detail_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @ord_id nvarchar(450) = NULL,
	@prod_id NVARCHAR(50) = NULL,
	@package_id nvarchar(50) = NULL,
	@start_dt NVARCHAR(100) = NULL,
	@end_dt NVARCHAR(100) = NULL,
	@type nvarchar(450) = NULL,
	@amount DECIMAL = null
AS
BEGIN TRY

    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF (@id IS NULL)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.order_details
		(
		    id,
		    ord_id,
		    prod_id,
		    package_id,
		    start_dt,
		    end_dt,
		    type,
		    amount,
		    created,
		    created_by
		)
		VALUES
		(   @id,     -- id - uniqueidentifier
		    @ord_id,   -- ord_id - uniqueidentifier
		    @prod_id,      -- prod_id - uniqueidentifier
		    @package_id,      -- package_id - uniqueidentifier
		   CONVERT(DATETIME,@start_dt,103), -- start_dt - datetime
		   CONVERT(DATETIME,@end_dt,103), -- end_dt - datetime
		    @type,      -- type - uniqueidentifier
		    @amount,      -- amount - decimal(18, 0)
		    GETDATE(), -- created - datetime
		    @userId
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.[order_details]
		SET ord_id = @ord_id,
			prod_id = @prod_id,
			package_id = @package_id,
			start_dt = CONVERT(DATETIME,@start_dt,103),
			end_dt = CONVERT(DATETIME,@end_dt,103),
			[type] = @type,
			amount = @amount,
			updated_by = @userId,
			updated = GETDATE()
        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_detail_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order_detail',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

