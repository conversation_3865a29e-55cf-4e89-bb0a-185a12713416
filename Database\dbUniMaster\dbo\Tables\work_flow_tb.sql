CREATE TABLE [dbo].[work_flow_tb] (
    [wft_id]     UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [wft_st]     INT              NOT NULL,
    [wft_dt]     DATETIME         NULL,
    [wft_type]   NVARCHAR (50)    NOT NULL,
    [wft_name]   NVARCHAR (50)    NULL,
    [tnx_id]     UNIQUEIDENTIFIER NOT NULL,
    [tnx_ref_no] NVARCHAR (50)    NULL,
    [tnx_object] NVARCHAR (200)   NULL,
    [tnx_remart] NVARCHAR (200)   NULL,
    [tnx_reason] NVARCHAR (100)   NULL,
    [report_to]  UN<PERSON>QUE<PERSON>ENTIFIER NULL,
    [mkr_id]     UNIQUEIDENTIFIER NOT NULL,
    [mkr_dt]     DATETIME         CONSTRAINT [DF_work_flow_tb_wft_mkr_dt] DEFAULT (getdate()) NULL,
    [auth_id]    UNIQUEIDENTIFIER NULL,
    [auth_dt]    DATETIME         NULL,
    [two_auth]   BIT              NULL,
    CONSTRAINT [PK_work_flow_tb] PRIMARY KEY CLUSTERED ([wft_id] ASC)
);


GO

