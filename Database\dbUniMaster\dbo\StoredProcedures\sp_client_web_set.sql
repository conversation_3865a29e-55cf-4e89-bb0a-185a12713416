


CREATE procedure [dbo].[sp_client_web_set]
     @userId nvarchar(450)
	,@webId uniqueidentifier
	,@webKey nvarchar(50)
	,@WebUrl nvarchar(250)
	,@WebName nvarchar(200)
	,@Description nvarchar(350)
	,@IconUrl nvarchar(250)
	,@Status bit
	,@clientId nvarchar(250)
	,@clientIdDev nvarchar(50)
	,@mod_cd nvarchar(50)
	,@isTab bit
as
begin
	declare @valid int = 1
	declare @messages nvarchar(500) =N'Thành công' 

	begin try	
	
	if not exists(select webId from Client_Webs WHERE webId = @webId)
	    begin
			if exists(select 1 from [client_webs] where webKey = @webKey)
			begin
				set @valid = 0
				set @messages = N'Mã số đã được sử dụng'
				goto FINAL
			end
			if exists(select 1 from [client_webs] where webName = @webName)
			begin
				set @valid = 0
				set @messages = N'Tên đã được sử dụng'
				goto FINAL
			end

			set @webId = newid();
        
			insert into [dbo].[client_webs]
				   (webId
				   ,[WebKey]
				   ,[WebUrl]
				   ,[WebName]
				   ,[Description]
				   ,[IconUrl]
				   ,created_dt
				   ,[Status]
				   ,[clientId]
				   ,clientIdDev
				   ,mod_cd
				   ,isTab
				   )
			 values
				   (@webId
				   ,@webKey
				   ,@WebUrl
				   ,@WebName
				   ,@Description
				   ,@IconUrl
				   ,getdate()
				   ,@Status
				   ,@clientId
				   ,@clientIdDev
				   ,@mod_cd
				   ,@isTab
				   )
			set @messages = N'Thêm mới thành công'
		end
	 else
	    begin
			if exists(select 1 from [client_webs] where webKey = @webKey and webId <> @webId)
			begin
				set @valid = 0
				set @messages = N'Mã số đã được sử dụng'
				goto FINAL
			end
			if exists(select 1 from [client_webs] where webName = @webName and webId <> @webId)
			begin
				set @valid = 0
				set @messages = N'Tên đã được sử dụng'
				goto FINAL
			end

			update [dbo].[Client_Webs]
			   set [WebKey] = @webKey
				  ,[WebUrl] = @WebUrl
				  ,[WebName] = @WebName
				  ,[Description] = @Description
				  ,[IconUrl] = @IconUrl
				  ,[Status] = @Status
				  ,[clientId] = @clientId
				  ,[clientIdDev] = @clientIdDev
				  ,[mod_cd] = @mod_cd
				  ,[isTab] = @isTab
			 where webId = @webId
			 set @messages = N'Cập nhật thành công'
         end
	
	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Web_Set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@WebName ' + @WebName
		set @valid = 0
		set @messages =  error_message()
		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'ClientRole', 'Insert', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as valid
		  ,@messages as [messages]
		  ,@webId as WebId
end

--select * from utl_error_log where TableName ='ClientRole'

GO

