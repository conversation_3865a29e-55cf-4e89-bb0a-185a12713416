
-- =============================================
-- Author: AnhTT
-- Create date: 
-- Description:	Chi tiết
-- Output: 
-- =============================================
CREATE PROCEDURE [dbo].[sp_customer_products_fields] @userId NVARCHAR(50) = NULL
    , @id UNIQUEIDENTIFIER = NULL
    , @customerId UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    DECLARE @table_key VARCHAR(50) = 'customer_products'
    DECLARE @groupKey VARCHAR(50) = 'common_group'

    --begin
    --1 thong tin chung
    SELECT @id id
        , tableKey = @table_key
        , groupKey = @groupKey

    --2- cac group
    SELECT *
    FROM [dbo].[fn_get_field_group](@groupKey)
    ORDER BY intOrder

    --
    SELECT [a].[customer_id]
        , [product_id] = dbo.fn_guid_to_string(a.product_id)
        , modules = (
            SELECT STRING_AGG(dbo.fn_guid_to_string(sa.module_id),',')
            FROM customer_product_module sa
            WHERE sa.customer_product_id = a.id
            )
        , [a].[start_date]
        , [a].[expire_date]
        , [a].[status]
        , [a].[reference]
        , [a].[database_id]
        , [a].[note]
        , [a].[created]
        , [a].[created_by]
        , [a].[updated]
        , [a].[updated_by]
    INTO #customer_products
    FROM customer_products a
    WHERE a.id = @id

    IF @id IS NULL
        INSERT INTO #customer_products (customer_id)
        VALUES (@customerId)

    --
    EXEC sp_get_data_fields @id
        , '#customer_products'
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_products_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , @table_key
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

