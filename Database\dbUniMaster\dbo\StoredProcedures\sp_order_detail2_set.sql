CREATE PROCEDURE [dbo].[sp_order_detail2_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @ord_id NVARCHAR(450) = NULL,
    @prod_id NVARCHAR(50) = NULL,
    @package_id NVARCHAR(50) = NULL,
    @start_dt NVARCHAR(100) = NULL,
    @end_dt NVARCHAR(100) = NULL,
    @type NVARCHAR(450) = NULL,
    @amount DECIMAL = NULL,
    ---
    @userName NVARCHAR(50) = NULL,
    @password NVARCHAR(50) = NULL,
    @assign NVARCHAR(500) = NULL
AS
BEGIN
    -- BEGIN TRANSACTION;
    BEGIN TRY

         DECLARE @valid BIT = 0,
                @messages NVARCHAR(250);

    --     IF (@id IS NULL)
    --     BEGIN
    --         SET @id = NEWID();
    --         -- insert order details
    --         INSERT INTO dbo.order_details
    --         (
    --             id,
    --             ord_id,
    --             prod_id,
    --             package_id,
    --             start_dt,
    --             end_dt,
    --             type,
    --             amount,
    --             created,
    --             created_by
    --         )
    --         VALUES
    --         (   @id,                               -- id - uniqueidentifier
    --             @ord_id,                           -- ord_id - uniqueidentifier
    --             @prod_id,                          -- prod_id - uniqueidentifier
    --             @package_id,                       -- package_id - uniqueidentifier
    --             CONVERT(DATETIME, @start_dt, 103), -- start_dt - datetime
    --             CONVERT(DATETIME, @end_dt, 103),   -- end_dt - datetime
    --             @type,                             -- type - uniqueidentifier
    --             @amount,                           -- amount - decimal(18, 0)
    --             GETDATE(),                         -- created - datetime
    --             @userId);
    --         -- insert order_account
    --         INSERT INTO dbo.order_account
    --         (
    --             ordDetails_id,
    --             login_uni_id,
    --             login_uni_pass,
    --             created,
    --             created_by
    --         )
    --         VALUES
    --         (   @id,       -- ordDetails_id - uniqueidentifier
    --             @userName, -- login_uni_id - nvarchar(50)
    --             @password, -- login_uni_pass - nvarchar(50)
    --             GETDATE(), -- created - datetime
    --             NULL);
    --         -- insert orderDetail_curator
    --         IF (@assign != '')
    --         BEGIN
    --             INSERT INTO dbo.orderDetails_curator
    --             (
    --                 ordDetail_id,
    --                 curator_id,
    --                 created,
    --                 created_by
    --             )
    --             SELECT @id,
    --                    t.Value,
    --                    GETDATE(),
    --                    @userId
    --             FROM dbo.fn_SplitString(@assign, ',') t;
    --         END;
    --         --
    --         SET @valid = 1;
    --         SET @messages = N'Thêm mới thành công';
    --     END;
    --     ELSE
    --     BEGIN
    --         -- update order details
    --         UPDATE dbo.[order_details]
    --         SET ord_id = @ord_id,
    --             prod_id = @prod_id,
    --             package_id = @package_id,
    --             start_dt = CONVERT(DATETIME, @start_dt, 103),
    --             end_dt = CONVERT(DATETIME, @end_dt, 103),
    --             [type] = @type,
    --             amount = @amount,
    --             updated_by = @userId,
    --             updated = GETDATE()
    --         WHERE id = @id;
    --         -- update order_account
    --         UPDATE dbo.order_account
    --         SET login_uni_id = @userName,
    --             login_uni_pass = @password,
    --             updated_by = @userId,
    --             updated = GETDATE()
    --         WHERE ordDetails_id = @id;
    --         -- update orderDetail_curator 
    --         IF EXISTS (SELECT 1 FROM dbo.orderDetails_curator WHERE ordDetail_id = @id)
    --         BEGIN
    --             DELETE FROM dbo.orderDetails_curator
    --             WHERE ordDetail_id = @id;
    --         END;
    --         INSERT INTO dbo.orderDetails_curator
    --         (
    --             ordDetail_id,
    --             curator_id,
    --             created,
    --             created_by
    --         )
    --         SELECT @id,
    --                t.Value,
    --                GETDATE(),
    --                @userId
    --         FROM dbo.fn_SplitString(@assign, ',') t;
    --         --
    --         SET @valid = 1;
    --         SET @messages = N'Cập nhật thành công';
    --     END;
	-- 	-- if not error, commit the transcation
    --     COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
		 -- if error, roll back any chanegs done by any of the sql statements
        ROLLBACK TRANSACTION;
        SELECT @messages AS [messages];
        DECLARE @ErrorNum INT,
                @ErrorMsg VARCHAR(200),
                @ErrorProc VARCHAR(50),
                @SessionID INT,
                @AddlInfo VARCHAR(MAX);

        SET @ErrorNum = ERROR_NUMBER();
        SET @ErrorMsg = 'sp_order_detail2_set' + ERROR_MESSAGE();
        SET @ErrorProc = ERROR_PROCEDURE();

        SET @AddlInfo = '@Userid' + @userId;

        EXEC utl_errorlog_set @ErrorNum,
                              @ErrorMsg,
                              @ErrorProc,
                              'order_detail',
                              'Set',
                              @SessionID,
                              @AddlInfo;
    END CATCH;
END;
FINAL:
SELECT @valid valid,
       @messages AS [messages];

GO

