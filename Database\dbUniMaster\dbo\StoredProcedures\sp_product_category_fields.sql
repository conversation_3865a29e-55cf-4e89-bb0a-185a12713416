
CREATE PROCEDURE [dbo].[sp_product_category_fields]
     @userId NVARCHAR(50) = NULL
    ,@id UNIQUEIDENTIFIER = NULL
	,@prod_line_id UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    --
    IF @id IS NOT NULL
       AND NOT EXISTS
    (
        SELECT 1
        FROM [dbo].product_category
        WHERE id = @id
    )
        SET @id = NULL;
    --begin
	SELECT *
	INTO #product_category
	FROM product_category
	WHERE id = @id

    --1 thong tin chung
    SELECT @id [id];
    --2- cac group
    SELECT  *
	FROM [dbo].fn_get_field_group('common_group') 
    --3 tung o trong group
    --exec sp_get_data_fields @id,'product_category'
	if @id IS NOT NULL
	begin

		SELECT a.[id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			 ,CASE [data_type] 
				  WHEN 'nvarchar' THEN CONVERT(NVARCHAR(350), CASE [field_name] 
                             WHEN 'category_name' THEN b.category_name
                             WHEN 'category_desc' THEN b.category_desc
						END) 
				  WHEN 'datetime' THEN CASE [field_name] 
							WHEN 'created' THEN FORMAT(b.created,'dd/MM/yyyy') END
                            WHEN 'updated' THEN FORMAT(b.updated,'dd/MM/yyyy')
                 
				 WHEN 'uniqueidentifier' THEN convert(NVARCHAR(350), CASE [field_name] 
							WHEN 'id' THEN (cast(b.id AS VARCHAR(50))) 
                            WHEN 'prod_line_id' THEN (cast(b.prod_line_id AS VARCHAR(50))) 
					  END)
                ELSE convert(NVARCHAR(50), CASE [field_name] 
					  WHEN 'int_ord' THEN b.int_ord
					  WHEN 'app_st' THEN b.app_st
					  END)
				END AS columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject]  = case when field_name = 'prod_line_id' then [columnObject] + lower(cast(b.prod_line_id as varchar(100))) else [columnObject] end
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM #product_category b
			OUTER APPLY
				(
					SELECT *
					FROM sys_config_form
					WHERE table_name = 'product_category' 
				)a
		  where 
			(isvisiable = 1 or isRequire = 1)
			
		  order by ordinal
	
	end
	else
	begin
		
		SELECT [id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,group_cd
			  ,case when a.field_name ='prod_line_id' then lower(cast(@prod_line_id as nvarchar(50)))
                    --when a.field_name ='package_name' then @package_name  
					else columnDefault end as columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject] = case when field_name = 'prod_line_id' then [columnObject] + lower(cast(@prod_line_id as varchar(100))) else [columnObject] end
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM sys_config_form a
		  where table_name = 'product_category' 
		  and (isvisiable = 1 or isRequire = 1)
		  order by ordinal

		--2
	end
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_category_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'Product Line',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

