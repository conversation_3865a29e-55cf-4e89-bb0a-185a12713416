
CREATE PROCEDURE [dbo].[sp_product_line_list] 
	@id uniqueidentifier = NULL
AS
BEGIN TRY
    --
    SELECT LOWER(CONVERT(NVARCHAR(500), id)) AS value,
           prod_line_name AS name
    FROM dbo.product_line l
    WHERE (
              @id IS NULL or l.id = @id
              --OR (dbo.ufn_removeMark(prod_line_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
          )
    ORDER BY prod_line_name;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_line_list_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_line',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

