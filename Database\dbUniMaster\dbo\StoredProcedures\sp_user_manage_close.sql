





CREATE procedure [dbo].[sp_user_manage_close]
	@manId	nvarchar(450),
	@userId	nvarchar(450)
as
begin
	declare @valid bit =  1
	declare @messages nvarchar(500) = N'Xóa người dùng thành công'

	begin try		
		--if exists (select 1 from client_users where userId = @userId 
		--	and work_st = 2
		--	)
		--begin
		--	set @valid = 0
		--	set @messages = N'Nhóm quyền đã được sử dụng ! Không được phép xóa'
		--	goto FINAL
		--end
			
		update u set 
			lock_st = 1
			,work_st = 4
			,delete_st = 1
			,delete_by = @manId
			,delete_dt = getdate()
		FROM client_users u
		WHERE userId = @userId 
		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_Del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@User ' 
		set @valid = 0
		set @messages = error_message()

		exec utl_ErrorLog_set @ErrorNum, @ErrorMsg, @ErrorProc, 'User_Manage', 'Del', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as valid, @messages as [messages]

		select u.userId
			  ,u.fullName
			  ,u.loginName as userName
			  ,channel = ''
			  ,'' as prod_type
			  ,active = case when u.work_st = 2 then 1 else 0 end
		FROM client_users u 
			where u.userId = @userId 

end

GO

