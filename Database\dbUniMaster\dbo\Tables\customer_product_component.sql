CREATE TABLE [dbo].[customer_product_component] (
    [id]                         UNIQ<PERSON><PERSON>ENTIFIER CONSTRAINT [DF_customer_product_component_id] DEFAULT (newid()) NOT NULL,
    [customer_product_module_id] UNIQUEIDENTIFIER NULL,
    [domain]                     NVARCHAR (250)   NULL,
    [status]                     TINYINT          NULL,
    [created]                    DATETIME         CONSTRAINT [DF_customer_product_component_created] DEFAULT (getdate()) NULL,
    [created_by]                 UNIQUEIDENTIFIER NULL,
    [updated]                    DATETIME         NULL,
    [updated_by]                 UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_customer_product_component_id] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_customer_product_component_customer_product_module] FOREIGN KEY ([customer_product_module_id]) REFERENCES [dbo].[customer_product_module] ([id])
);


GO

