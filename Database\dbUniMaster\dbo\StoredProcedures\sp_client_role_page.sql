

CREATE procedure [dbo].[sp_client_role_page]
	@UserId			nvarchar(50),
	@webId			nvarchar(200),
	@filter			nvarchar(100),
	@gridWidth		int		= 0,
	@Offset			int				= 0,
	@PageSize		int				= 50,
	@Total			bigint out,
	@TotalFiltered	bigint out,
	@GridKey		nvarchar(100) out

as
	begin try
		    --BEGIN
      --          Update t
      --          set t.last_dt = getdate()
      --          from client_users t
      --          where userId = @UserId
      --      END
			if @UserId is null set @UserId = '81739c5c-2ca0-4e0f-acab-63373ea8a34a'

			set		@Offset					= isnull(@Offset, 0)
			set		@PageSize				= isnull(@PageSize, 50)
			set		@Total					= isnull(@Total, 0)
			set		@filter					= isnull(@filter,'')

			if		@PageSize	= 0			set @PageSize	= 10
			if		@Offset		< 0			set @Offset		=  0

			set		@gridKey				= 'view_Client_Web_Role_Page'

			select @Total	= count(a.webRoleId)
				from [client_web_role] a
					join client_webs b on a.webId = b.webid
					where b.WebId = @webId

				set @TotalFiltered = @Total

			if @Offset = 0
				begin
					select * from [dbo].fn_config_list_gets (@gridKey, @gridWidth) 
					order by [ordinal]
				end

			select a.webRoleId 
				,a.[webId]
				,a.[roleCd]
				--,a.[WebKey]
				,a.[roleName]
				,a.created_dt
				,b.webName
				,countUse = (select count(*) from client_web_user where webRoleId = a.webRoleId)
			from [dbo].[client_web_role] a
			join client_webs b on a.webId = b.webid
			where b.WebId = @webId
			 and roleName like '%' + @filter + '%'
			  
			 ----11--
			 --SELECT [menuRoleId]
				--  ,a.[menuId]
				--  ,a.[webRoleId]
				--  ,a.intPos
				--  ,c.roleCd as roleCd
				--  ,b.title as title
			 -- FROM [dbo].[client_role_menu] a 
				--join client_web_menu b on a.menuId = b.menuId
				--left join client_web_role c on a.webRoleId = c.webRoleId
			 --where b.webId = @webId
			 --order by b.intPos

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Role_Page ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'MenuPage', 'Get', @SessionID, @AddlInfo
	end catch

GO

