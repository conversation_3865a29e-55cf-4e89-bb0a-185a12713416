

CREATE procedure [dbo].[sp_dashboard_timline_get]
as
	begin try
		DECLARE @timeline TABLE
   ([icon] NVARCHAR(250),
   bgClass NVARCHAR(50),
   [action] NVARCHAR(100),
   [time] NVARCHAR(250),
   [create_time] datetime,
   createdBy NVARCHAR(30))

   INSERT INTO @timeline
   SELECT 'fa-folder-plus' as icon,
          'bg-primary' bgClass,
          N'Tạo quyền ' + '[' + a.roleName + ']' as action,
          dbo.fn_Get_TimeAgo(a.created_dt) [time],
          a.created_dt as create_time,
          b.fullname as createdBy
   FROM client_web_role a left join client_users b on a.created_by = b.userId
   ORDER BY a.created_dt DESC

   INSERT INTO @timeline
   SELECT 'fa-tree' as icon,
          'bg-green' bgClass,
          N'Tạo user ' + '[' +a.fullName + ']' as action,
          dbo.fn_Get_TimeAgo(a.created_dt) [time],
          a.created_dt as create_time,
          b.fullname as createdBy
   FROM client_users a left join client_users b on a.created_by = b.userId
   ORDER BY a.created_dt DESC

   INSERT INTO @timeline
   SELECT 'fa-tree' as icon,
          'bg-green' bgClass,
          '[' + c.roleName + ']'  + N' đã được phân cho ' + '[' + d.fullName + ']' as action,
          dbo.fn_Get_TimeAgo(a.creationTime) [time],
          a.creationTime as create_time,
          b.fullname as createdBy
   FROM client_role_user a left join client_users b on a.creationBy = b.userId
                           left join client_web_role c on a.webRoleId = c.webRoleId
                           left join client_users d on a.userId = d.userId
   ORDER BY a.creationTime DESC

   SELECT TOP 20 * FROM @timeline ORDER BY create_time DESC
			   

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_dashboard_timline_get ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'sp_dashboard_timline_get', 'Get', @SessionID, @AddlInfo
	end catch

GO

