CREATE PROCEDURE [dbo].[sp_customer_register_fields]
    @userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = NULL,
    @action nvarchar(20) = NULL,
    @cust_type int = NULL
AS
BEGIN TRY

    DECLARE @table_key VARCHAR(50) = 'customer_register'
    --
    SELECT* 
	INTO #customers
	FROM customer where id = @id
    --begin
    --1 thong tin chung
    
	SELECT @id gd
		   ,tableKey			= @table_key
		   ,groupKey			= 'mas_customers_group'
    --2- cac group
    SELECT *
	FROM [dbo].[fn_get_field_group] ('mas_customers_group_update') 
	ORDER BY intOrder	
    --3 tung o trong group
    EXEC sp_get_data_fields null,@table_key
    
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'Customer',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

