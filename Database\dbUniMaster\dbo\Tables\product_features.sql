CREATE TABLE [dbo].[product_features] (
    [id]           UNIQ<PERSON><PERSON>ENTIFIER CONSTRAINT [DF_product_features_id] DEFAULT (newid()) NOT NULL,
    [prod_id]      UNIQUEIDENTIFIER NULL,
    [feature_name] <PERSON>VA<PERSON>HA<PERSON> (50)    NULL,
    [feature_desc] NVA<PERSON>HAR (150)   NULL,
    [icon]         NVA<PERSON>HA<PERSON> (200)   NULL,
    [image1]       NVARCHAR (200)   NULL,
    [image2]       NVARCHAR (200)   NULL,
    [image3]       NVARCHAR (200)   NULL,
    [created]      DATETIME         NULL,
    [created_by]   UN<PERSON>Q<PERSON><PERSON>ENTIFIER NULL,
    [updated]      <PERSON><PERSON><PERSON><PERSON><PERSON>         NULL,
    [updated_by]   <PERSON><PERSON><PERSON><PERSON><PERSON>ENTIFIER NULL,
    [common_is]    BIT              NULL,
    CONSTRAINT [PK_product_features] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_product_features_products] FOREIGN KEY ([prod_id]) REFERENCES [dbo].[products] ([id])
);


GO

