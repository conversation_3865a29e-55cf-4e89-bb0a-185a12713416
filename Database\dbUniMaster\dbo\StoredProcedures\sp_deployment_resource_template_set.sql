
-- =============================================
-- Author:		AnhTT
-- Create date: 2024-04-122
-- Description:	Add/update component resource
-- =============================================
CREATE PROCEDURE [dbo].[sp_deployment_resource_template_set] @userId UNIQUEIDENTIFIER = NULL
    , @id UNIQUEIDENTIFIER = NULL
    , @product_component_id UNIQUEIDENTIFIER = NULL
    , @repository_id BIGINT = NULL
    , @name NVARCHAR(250)
    , @kind NVARCHAR(100)
    , @content NVARCHAR(MAX)
    , @ordinal INT
    , @status INT
AS
BEGIN
    DECLARE @valid BIT = 1
    DECLARE @message NVARCHAR(100) = N''
    DECLARE @product_id UNIQUEIDENTIFIER = (
            SELECT product_id
            FROM deployment_product_component
            WHERE id = @product_component_id
            )

    BEGIN TRY
        IF @id IS NULL
        BEGIN
            SET @message = N'Thêm mới thành công'

            GOTO FINAL;
        END
        SELECT * FROM deployment_resource_template
        UPDATE deployment_resource_template
        SET name = @name
            , kind = @kind
            , product_id = @product_id
            , product_component_id = @product_component_id
            , content = @content
            , ordinal = @ordinal
            , [status] = @status
            , updated = GETDATE()
        WHERE id = @id

        SET @message = N'Cập nhậtthành công'
    END TRY

    BEGIN CATCH
        DECLARE @ErrorNum INT
            , @ErrorMsg VARCHAR(200)
            , @ErrorProc VARCHAR(50)
            , @SessionID INT
            , @AddlInfo VARCHAR(max)

        SET @ErrorNum = error_number()
        SET @ErrorMsg = 'sp_deployment_resource_template_set ' + error_message()
        SET @ErrorProc = error_procedure()
        SET @AddlInfo = LOWER(@userId)
        SET @valid = 0
        SET @message = error_message()

        EXEC utl_ErrorLog_Set @ErrorNum
            , @ErrorMsg
            , @ErrorProc
            , 'User'
            , 'Set'
            , @SessionID
            , @AddlInfo
    END CATCH

    FINAL:

    SELECT @valid AS valid
        , @message AS [messages]
        , @id id
END

GO

