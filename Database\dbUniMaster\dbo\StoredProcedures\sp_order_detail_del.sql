CREATE PROCEDURE [dbo].[sp_order_detail_del]
    @userId NVARCHAR(50),
    @id UNIQUEIDENTIFIER
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF NOT EXISTS (SELECT 1 FROM dbo.[order_details] WHERE id = @id)
    BEGIN
        SET @messages = N'Bản ghi không tồn tại';
        GOTO FINAL;
    END;

    IF EXISTS (SELECT TOP 1 1 FROM dbo.[contract] WHERE ord_dts_id = @id)
    BEGIN
        SET @messages = N'Tồn tại hợp đồng của đơn hàng này. Không thể xoá';
        GOTO FINAL;
    END;
	
    --
    DELETE FROM dbo.order_details
    WHERE id = @id;
    --
    SET @valid = 1;
    SET @messages = N'Xóa thành công';
    --
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];
END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_detail_del' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order_detail',
                          'DEL',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

