CREATE PROCEDURE [dbo].[sp_products_page]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
	@product_line_id NVARCHAR(450) = NULL,
	@customer_Id NVARCHAR(450) = NULL,

    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(DISTINCT p.id)
    FROM dbo.products p
	LEFT JOIN dbo.product_line pl ON pl.id = p.prod_line_id
	LEFT JOIN dbo.order_details od ON od.prod_id = p.id
	LEFT JOIN dbo.[order] o ON o.id = od.ord_id
    WHERE dbo.ufn_removeMark(prod_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%'
	AND (@product_line_id IS NULL OR @product_line_id = pl.id)
	AND (@customer_Id IS NULL OR @customer_Id = o.cust_id)
          

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_products_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data
	SELECT tempB.*,tempA.sl_khach
	FROM 
	((SELECT p.id,COUNT(DISTINCT(cus.id)) AS sl_khach
	FROM dbo.products p
	LEFT JOIN dbo.product_line pl ON pl.id = p.prod_line_id
	LEFT JOIN dbo.order_details ord ON ord.prod_id = p.id
	LEFT JOIN [order] o ON o.id = ord.ord_id
	LEFT JOIN dbo.customer cus ON cus.id = o.cust_id
	GROUP BY p.id) tempA
	INNER JOIN (
	SELECT DISTINCT p.id,p.prod_name,p.prod_desc,p.prod_line_id,pl.prod_line_name,p.icon,p.created,p.created_by,p.updated,p.updated_by
	FROM dbo.products p
	LEFT JOIN dbo.product_line pl ON pl.id = p.prod_line_id
	LEFT JOIN dbo.order_details ord ON ord.prod_id = p.id
	LEFT JOIN [order] o ON o.id = ord.ord_id
	--LEFT JOIN dbo.customers cus ON cus.id = o.cust_id
	WHERE dbo.ufn_removeMark(p.prod_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		AND (@product_line_id IS NULL OR @product_line_id = pl.id)
		AND (@customer_Id IS NULL OR @customer_Id = o.cust_id)
	) tempB ON tempB.id = tempA.id)
	ORDER BY CASE
                 WHEN tempB.updated > tempB.created THEN
                     tempB.updated
                 ELSE
                     tempB.created
             END DESC,
             tempB.prod_name DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_line_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

