
CREATE PROCEDURE [dbo].[sp_organization_page] @userId NVARCHAR(450)
    , @filter NVARCHAR(250)
    , @gridWidth INT = 0
    , @Offset INT = 0
    , @PageSize INT = 10
    , @Total INT OUT
    , @TotalFiltered INT OUT
    , @gridKey NVARCHAR(100) OUT
AS
BEGIN TRY
    SET @Offset = isnull(@Offset, 0)
    SET @PageSize = isnull(@PageSize, 10)
    SET @Total = isnull(@Total, 0)
    SET @filter = isnull(@filter, '')

    IF @PageSize = 0
        SET @PageSize = 10

    IF @Offset < 0
        SET @Offset = 0
    SET @gridKey = 'view_organization_Page'

    SELECT @Total = count(1)
    FROM organization a
    WHERE a.name LIKE @filter + '%'

    SET @TotalFiltered = @Total

    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets(@gridKey, @gridWidth)
        ORDER BY [ordinal]
    END

    --
    SELECT [a].[id]
        , [a].[code]
        , [a].[name]
        , [a].[address]
        , [a].[status]
    FROM organization a
    WHERE a.name LIKE @filter + '%'
    ORDER BY a.[code] offset @Offset rows

    FETCH NEXT @PageSize rows ONLY
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(max)

    SET @ErrorNum = error_number()
    SET @ErrorMsg = 'sp_organization_page ' + error_message()
    SET @ErrorProc = error_procedure()
    SET @AddlInfo = ' '

    EXEC utl_ErrorLog_Set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'organization'
        , 'GET'
        , @SessionID
        , @AddlInfo
END CATCH

GO

