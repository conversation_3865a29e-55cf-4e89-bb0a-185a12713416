




CREATE procedure [dbo].[sp_work_flow_submit]
	@UserId			nvarchar(450),
	@wft_type		nvarchar(50),
	@tnx_id			uniqueidentifier,
	@tnx_ref_no		nvarchar(100),
	@tnx_remart		nvarchar(200),
	@tnx_object		nvarchar(300),
	@reportTo		uniqueidentifier
as
begin
	declare @valid bit = 1
	declare @messages nvarchar(400) = ''
	declare @submit_st bit = 1
	begin try			
		
	--BEGIN TRAN COR_Workflow_Submit_trans
	if @wft_type = 'user_info'
		begin
			 --@submit_st
			 if @submit_st = 1
			 begin
				 INSERT INTO [dbo].work_flow_tb
					  ([wft_st]
					  ,[wft_dt]
					  ,[wft_type]
					  ,[wft_name]
					  ,[tnx_id]
					  ,[tnx_ref_no]
					  ,[tnx_remart]
					  ,[tnx_reason]
					  ,[tnx_object]
					  ,[mkr_id]
					  ,[report_to]
					  )
				 VALUES
					   (0
					   ,getdate()
					   ,@wft_type
					   ,N'<PERSON><PERSON><PERSON> cầu duyệt người dùng'
					   ,@tnx_id
					   ,@tnx_ref_no
					   ,@tnx_remart
					   ,null
					   ,@tnx_object
					   ,@UserId
					   ,@reportTo
					   )
			 
				set @messages = N'Trình duyệt người dung thành công!'
			 end
			 else
				Begin
					set @valid = 0
					set @messages = N'Thông tin không hợp lệ , giao dịch đang trình duyệt hoặc đã duyệt'
				end
		end
		else if @wft_type = 'user_role'
		begin
			--submit

				
			if @submit_st = 1
			 begin
			 
				 INSERT INTO [dbo].work_flow_tb
					  ([wft_st]
					  ,[wft_dt]
					  ,[wft_type]
					  ,[wft_name]
					  ,[tnx_id]
					  ,[tnx_ref_no]
					  ,[tnx_remart]
					  ,[tnx_reason]
					  ,[tnx_object]
					  ,[mkr_id]
					  ,[report_to]
					  )
				 VALUES
					   (0
					   ,getdate()
					   ,@wft_type
					   ,N'Yêu cầu duyệt phân quyền'
					   ,@tnx_id
					   ,@tnx_ref_no
					   ,@tnx_remart
					   ,null
					   ,@tnx_object
					   ,@UserId
					   ,@reportTo
					   )
			 
				set @messages = N'Trình duyệt phân quyền!'
			 end
			 else
				Begin
					set @valid = 0
					set @messages = N'Thông tin không hợp lệ, giao dịch đang trình duyệt hoặc đã duyệt'
				end
		end
		else
		Begin
			set @valid = 0
			set @messages = N'Không áp dụng mã ' + @wft_type
		end
	

	--COMMIT TRAN COR_Workflow_Submit_trans
	end try
	begin catch
	--ROLLBACK TRAN COR_Workflow_Submit_trans

		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_work_Workflow_Submit' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''
		set @valid = 0
		set @messages = error_message()

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'profilesubmit', 'DEL', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as [valid]
		  ,@messages as [messages]
	end

GO

