CREATE TABLE [dbo].[customer_product_module] (
    [id]                  UNIQ<PERSON><PERSON>ENTIFIER CONSTRAINT [DF_customer_product_module_id] DEFAULT (newid()) NOT NULL,
    [customer_product_id] UNIQUEIDENTIFIER NULL,
    [module_id]           UNIQUE<PERSON>ENTIFIER NULL,
    [status]              TINYINT          NULL,
    [isActive]            BIT              NULL,
    [created]             DATETIME         CONSTRAINT [DF_customer_product_module_created] DEFAULT (getdate()) NULL,
    [created_by]          UNIQUEIDENTIFIER NULL,
    [updated]             DATETIME         NULL,
    [updated_by]          <PERSON><PERSON><PERSON><PERSON><PERSON>ENTIFIER NULL,
    CONSTRAINT [PK_customer_product_module_id] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_customer_product_module_customer_product_id] FOREIGN KEY ([customer_product_id]) REFERENCES [dbo].[customer_products] ([id])
);


GO

