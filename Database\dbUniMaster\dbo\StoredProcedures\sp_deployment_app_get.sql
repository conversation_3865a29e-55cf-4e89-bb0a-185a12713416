
CREATE PROCEDURE [dbo].[sp_deployment_app_get] @UserId NVARCHAR(50)
    , @customerId UNIQUEIDENTIFIER
    , @productId UNIQUEIDENTIFIER
    , @tagId bigint
AS
BEGIN TRY
    --general deployment info
    SELECT [customerCode] =c.code  
        , CustomerName = c.full_name
        , tenantName = t.name
    FROM customer c
    LEFT JOIN deployment_tenant t ON c.id = t.customer_id
    WHERE c.id = @customerId
    -- components
    SELECT [id],
    [product_id],
    [code],
    [name],
    [repository_id]
    FROM deployment_product_component
    WHERE product_id = @productId

    -- resource templates
    SELECT [id],
    [product_id],
    ComponentId = [product_component_id],
    [name],
    [kind],
    [content],
    [ordinal]
    FROM deployment_resource_template
    WHERE product_id = @productId
    AND [status] = 1
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(max)

    SET @ErrorNum = error_number()
    SET @ErrorMsg = 'sp_deployment_app_get ' + error_message()
    SET @ErrorProc = error_procedure()
    SET @AddlInfo = ''

    EXEC utl_Insert_ErrorLog @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'ClientWebs,ClientWebRoles'
        , 'Get'
        , @SessionID
        , @AddlInfo
END CATCH

GO

