
-- =============================================
-- Author: AnhTT
-- Create date: 2025-04-23
-- Description:	<PERSON>h sách khách hàng
-- Output: 
-- =============================================
CREATE PROCEDURE [dbo].[sp_customer_page] @userId NVARCHAR(50) = NULL
    , @filter NVARCHAR(100) = NULL
    , @gridWidth INT = NULL
    , @Offset INT = 0
    , @PageSize INT = 10
    , @Total INT = 0 OUT
    , @gridKey NVARCHAR(50) OUT
    , @TotalFiltered INT = 0 OUT
AS
BEGIN TRY
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    --
    IF @PageSize = 0
        SET @PageSize = 10;

    IF @Offset < 0
        SET @Offset = 0;

    SELECT @total = COUNT(1)
    FROM customer t

    SET @TotalFiltered = @Total;

    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets('view_customer_page', 0)
        ORDER BY [ordinal];
    END;

    -- Data
    SELECT a.[id],
    a.[code],
    [cif_no],
    [cust_type] = t.objName,
    [full_name],
    [birth_day],
    [avatar],
    [sex] = g.objName,
    [per_address],
    [org_name],
    [establish_date],
    [tax],
    [fax],
    [postion],
    [phone_1],
    [phone_2],
    [email_1],
    [email_2],
    [org_address],
    [provinve] = p.name,
    [district] = d.name,
    [national] = n.name,
    [description],
    a.[created],-- = dbo.fn_format_date(a.created),
    [created_by] = a.created_by,
    [updated] =  dbo.fn_format_date(a.updated),
    [updated_by] = a.updated_by
    FROM customer a
    LEFT JOIN [dbo].[fn_config_data_gets]('cust_type') t ON a.cust_type = t.objValue
    LEFT JOIN [dbo].[fn_config_data_gets]('sex') g ON a.sex = g.objValue
    LEFT JOIN [national] n ON n.id = a.national_id
    LEFT JOIN province p ON p.id = a.provinve_id
    LEFT JOIN district d ON a.district_id = d.id
    ORDER BY cif_no DESC OFFSET @Offset ROWS

    FETCH NEXT @PageSize ROWS ONLY;
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_page ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'customer'
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

