






CREATE procedure [dbo].[sp_client_role_bywebid]
	@UserId nvarchar(50) = null,
	@webId	uniqueidentifier ='F64B0750-2B1D-4011-89FF-36642C1D65BE'
as
	begin try
			
			SELECT b.RoleCd
				  ,cast(b.<PERSON><PERSON>oleId as nvarchar(50)) as value
				  ,b.WebId
				  ,b.isAdmin 
				  ,b.RoleName + '[' + b.roleCd + ']'  as name
				  ,c.<PERSON><PERSON> 
				  ,c.webName
			FROM [client_web_role] b 
				join client_webs c on b.webId = c.webId
			WHERE b.webId = @webId 
				
			

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_client_role_bywebid ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'RoleApps', 'Get', @SessionID, @AddlInfo
	end catch

    --select * from utl_error_log where TableName ='RoleApps'

GO

