CREATE PROCEDURE [dbo].[sp_app_profile_get]
	@userId	NVARCHAR(50) = NULL

AS
BEGIN TRY
    select a.avatar as avatar_url,
           a.fullname as full_name,
           N'UniCloud Company' as company_name,
           N'Nhân viên' as position_name,
           N'Sunshine Center' as work_location,
           a.email as email,
           convert(nvarchar(50),a.userId) as userId,
           a.phone
    from [User] a 
	--inner join dbSHRM.dbo.UserInfo b on a.phone = b.phone
    where a.userId = @userId

END TRY
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_app_profile_get ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserAuth '  + @userId 


		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'User', 'Set', @SessionID, @AddlInfo
	end catch

GO

