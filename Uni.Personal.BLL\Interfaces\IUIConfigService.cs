using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// UIConfig service interface
    /// </summary>
    public interface IUIConfigService
    {
        /// <summary>
        /// Get paginated list of UI configurations
        /// </summary>
        /// <param name="query">Filter input for UI configurations</param>
        /// <returns>Paginated list of UI configurations</returns>
        Task<CommonListPage> GetPageAsync(UIConfigFilterInput? query);

        /// <summary>
        /// Get UI configuration information by ID
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>UI configuration information</returns>
        Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);

        /// <summary>
        /// Create or update UI configuration
        /// </summary>
        /// <param name="info">UI configuration information</param>
        /// <returns>Validation result with UI configuration ID</returns>
        Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);

        /// <summary>
        /// Delete UI configuration
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> DeleteAsync(Guid? oid);
    }
}
