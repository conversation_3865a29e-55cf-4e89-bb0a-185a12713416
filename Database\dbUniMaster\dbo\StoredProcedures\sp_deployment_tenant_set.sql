
CREATE PROCEDURE [dbo].[sp_deployment_tenant_set] @userId UNIQUEIDENTIFIER = NULL
    , @id UNIQUEIDENTIFIER = NULL
    , @customer_id UNIQUEIDENTIFIER
    , @name NVARCHAR(250)
    , @namespace NVARCHAR(100)
    , @status NVARCHAR(50)
AS
BEGIN
    DECLARE @valid BIT = 1
    DECLARE @message NVARCHAR(100) = N''

    BEGIN TRY
        IF @id IS NULL
            OR NOT EXISTS (
                SELECT 1
                FROM deployment_tenant
                WHERE id = @id
                )
        BEGIN
            IF @id IS NULL
                SET @id = NEWID()

            INSERT INTO deployment_tenant (
                [id]
                , [customer_id]
                , [name]
                , [namesapce]
                , [status]
                )
            VALUES (
                @id
                , @customer_id
                , @name
                , @namespace
                , @status
                )

            SET @message = N'Thêm mới thành công'

            GOTO FINAL
        END

        --
        -- UPDATE deployment_tenant
        -- SET customer_id = @customer_id
        --     , name = @name
        --     , namesapce = @namespace
        --     , [status] = @status
        -- WHERE id = @id

        -- SET @message = N'Cập nhật thành công'
    END TRY

    BEGIN CATCH
        DECLARE @ErrorNum INT
            , @ErrorMsg VARCHAR(200)
            , @ErrorProc VARCHAR(50)
            , @SessionID INT
            , @AddlInfo VARCHAR(max)

        SET @ErrorNum = error_number()
        SET @ErrorMsg = 'sp_deployment_tenant_set ' + error_message()
        SET @ErrorProc = error_procedure()
        SET @AddlInfo = LOWER(@userId)
        SET @valid = 0
        SET @message = error_message()

        EXEC utl_ErrorLog_Set @ErrorNum
            , @ErrorMsg
            , @ErrorProc
            , 'User'
            , 'Set'
            , @SessionID
            , @AddlInfo
    END CATCH

    FINAL:

    SELECT @valid AS valid
        , @message AS [messages]
        , @id id
END

GO

