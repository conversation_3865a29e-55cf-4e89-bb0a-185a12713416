-- =============================================
-- Author:		AnhTT
-- Create date: 
-- Description:	Add/update thông tin khách hàng
-- =============================================
CREATE PROCEDURE [dbo].[sp_customer_set] @userId NVARCHAR(50)
    , @id UNIQUEIDENTIFIER
    , @code NVARCHAR(60) = NULL
    , @cif_no NVARCHAR(60) = NULL
    , @cust_type INT = NULL
    , @full_name NVARCHAR(200) = NULL
    , @birth_day NVARCHAR(50) = NULL
    , @avatar NVARCHAR(MAX) = NULL
    , @sex INT = NULL
    , @per_address NVARCHAR(200) = NULL
    , @org_name NVARCHAR(200) = NULL
    , @establish_date NVARCHAR(50) = NULL
    , @tax NVARCHAR(100) = NULL
    , @fax NVARCHAR(100) = NULL
    , @postion NVARCHAR(200) = NULL
    , @phone_1 NVARCHAR(40) = NULL
    , @phone_2 NVARCHAR(40) = NULL
    , @email_1 NVARCHAR(40) = NULL
    , @email_2 NVARCHAR(40) = NULL
    , @org_address NVARCHAR(200) = NULL
    , @provinve_id UNIQUEIDENTIFIER = NULL
    , @district_id UNIQUEIDENTIFIER = NULL
    , @national_id UNIQUEIDENTIFIER = NULL
    , @description NVARCHAR(500) = NULL
AS
BEGIN TRY
    DECLARE @valid BIT = 0
        , @messages NVARCHAR(250);

    IF @national_id IS NULL
        SELECT TOP 1 @national_id = id
        FROM [national]
        WHERE code = '84'
    IF (
            @id IS NULL
            OR NOT EXISTS (
                SELECT 1
                FROM customer
                WHERE id = @id
                )
            )
    BEGIN
        IF @id IS NULL
            SET @id = NEWID();

        -- insert
        INSERT INTO customer (
            [id]
            , [code]
            -- , [cif_no]
            , [cust_type]
            , [full_name]
            , [birth_day]
            , [avatar]
            , [sex]
            , [per_address]
            , [org_name]
            , [establish_date]
            , [tax]
            , [fax]
            , [postion]
            , [phone_1]
            , [phone_2]
            , [email_1]
            , [email_2]
            , [org_address]
            , [provinve_id]
            , [district_id]
            , [national_id]
            , [description]
            , created
            , created_by
            )
        VALUES (
            @id
            , @code
            -- , @cif_no
            , @cust_type
            , @full_name
            , CONVERT(DATETIME, @birth_day, 103)
            , @avatar
            , @sex
            , @per_address
            , @org_name
            , CONVERT(DATETIME, @establish_date, 103)
            , @tax
            , @fax
            , @postion
            , @phone_1
            , @phone_2
            , @email_1
            , @email_2
            , @org_address
            , @provinve_id
            , @district_id
            , @national_id
            , @description
            , GETDATE()
            , @userId
            )

        --
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
        UPDATE customer
        SET [id] = @id
            -- , [cif_no] = @cif_no
            , [code] = @code
            , [cust_type] = @cust_type
            , [full_name] = @full_name
            , [birth_day] = CONVERT(DATETIME, @birth_day, 103)
            , [avatar] = @avatar
            , [sex] = @sex
            , [per_address] = @per_address
            , [org_name] = @org_name
            , [establish_date] = CONVERT(DATETIME, @establish_date, 103)
            , [tax] = @tax
            , [fax] = @fax
            , [postion] = @postion
            , [phone_1] = @phone_1
            , [phone_2] = @phone_2
            , [email_1] = @email_1
            , [email_2] = @email_2
            , [org_address] = @org_address
            , [provinve_id] = @provinve_id
            , [district_id] = @district_id
            , [national_id] = @national_id
            , [description] = @description
            , updated_by = @userId
            , updated = GETDATE()
        WHERE id = @id;

        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;

    FINAL:

    SELECT @valid valid
        , @messages AS [messages]
        , [id] = @id;
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'customer'
        , 'SET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

