CREATE TABLE [dbo].[order_account] (
    [id]              UNIQUE<PERSON>ENTIFIER CONSTRAINT [DF_order_account_id] DEFAULT (newid()) NOT NULL,
    [ord_id]          UNIQUEIDENTIFIER NULL,
    [ord_dtl_id]      UNIQUEIDENTIFIER NULL,
    [login_uni_id]    NVARCHAR (50)    NULL,
    [login_uni_pass]  NVARCHAR (50)    NULL,
    [login_uni_alias] NVARCHAR (50)    NULL,
    [user_id]         UNIQUEIDENTIFIER NULL,
    [created]         DATETIM<PERSON>         NULL,
    [created_by]      <PERSON><PERSON><PERSON><PERSON><PERSON>ENTIFIER NULL,
    [updated]         D<PERSON><PERSON>IM<PERSON>         NULL,
    [updated_by]      <PERSON><PERSON><PERSON><PERSON><PERSON>ENTIFIER NULL,
    CONSTRAINT [PK_mas_order_account] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_order_account_order] FOREIGN KEY ([ord_id]) REFERENCES [dbo].[order] ([id])
);


GO

