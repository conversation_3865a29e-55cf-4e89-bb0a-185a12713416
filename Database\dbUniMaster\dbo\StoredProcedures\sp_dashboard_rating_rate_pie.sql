
CREATE PROCEDURE [dbo].[sp_dashboard_rating_rate_pie]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(150) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @fromDate DATETIME = '2023-05-01',
    @toDate DATETIME = '2023-05-30',
    @area_Id UNIQUEIDENTIFIER = NULL -- '67DC0318-23CF-4F67-B1A8-D6AE2059EBEB'
AS
BEGIN TRY
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @filter = ISNULL(@filter, '');

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;
	-- Lấy service_id của userId
    DECLARE @service_id UNIQUEIDENTIFIER;
    SELECT @service_id = service_id
    FROM dbo.[user]
    WHERE userId = @userId

    SELECT Total = 1,
           totalFiltered = 1;

    -- 1. <PERSON><PERSON> vote tương <PERSON>ng với mức độ đánh giá
    SELECT sa.answer_VN AS rate_name,
           COUNT(re.answer_id) AS num_rate
    --   FROM survey_result re
    --LEFT JOIN dbo.survey_answer sa ON sa.id = re.answer_id
    FROM survey_answer sa
        LEFT JOIN survey_result re
            ON sa.id = re.answer_id
        LEFT JOIN dbo.survey_question sq
            ON re.question_id = sq.id
    --LEFT JOIN survey_area a ON a.id = re.area_id
    WHERE sq.question_type = 0
          AND
          (
              @area_Id IS NULL
              OR @area_Id = re.area_id
          )
          AND
          (
              CAST(re.created AS DATE) >= CAST(@fromDate AS DATE)
              AND CAST(re.created AS DATE) <= CAST(@toDate AS DATE)
          )
          AND re.area_id IS NOT NULL
		  AND sq.service_id = @service_id
    GROUP BY answer_VN
--ORDER BY rate_Id

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_dashboard_rating_rate_pie' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' @user: ' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'sp_dashboard_rating_rate_pie',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

