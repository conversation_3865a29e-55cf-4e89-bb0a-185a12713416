




create procedure [dbo].[sp_client_web_del]
	@userId	nvarchar(50),
    @webId uniqueidentifier
as
begin
	declare @valid bit =  1
	declare @messages nvarchar(500) = N'Xóa client web thành công'

	begin try		
		if exists (select 1 from client_role_user where webId = @webId and work_st = 2 )
		begin
			set @valid = 0
			set @messages = N'Web đã được phân quyên quyền người dùng. Không thể xóa'
			goto FINAL
		end

		DELETE FROM client_webs
		WHERE webId = @webId

		--DELETE FROM client_web_user
		--WHERE userId = @userId
	
		--DELETE FROM client_users
		--WHERE userId = @userId 
		--and isnull(work_st,0) < 2

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_client_web_del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@User ' 
		set @valid = 0
		set @messages = error_message()

		exec utl_ErrorLog_set @ErrorNum, @ErrorMsg, @ErrorProc, 'client_webs', 'Del', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as valid, @messages as [messages]

end

GO

