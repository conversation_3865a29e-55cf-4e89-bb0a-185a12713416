

-- =============================================
-- Author:		
-- Description:	l<PERSON>y thông tin menu theo người dùng
-- =============================================
CREATE procedure [dbo].[sp_client_web_menus_byuserId]
	@UserId nvarchar(450) ='81739c5c-2ca0-4e0f-acab-63373ea8a34a',
	@WebId uniqueidentifier = '70e930b0-ffea-43d3-b3a9-0e6b03f2b433'
as
	begin try
		--declare @webRoleId uniqueidentifier 
		--		= (select top 1 u.webRoleId FROM client_role_user u 
		--					join client_webs w on u.WebId = w.webId 
		--			where u.UserId = @UserId 
		--				and u.WebId = @WebId 
		--				and u.work_st = 2)

		if @UserId is null or @UserId = '' set @UserId = '836C0067-8DCB-4D00-91DF-63A6595144C9'
		else exec utl_Insert_ErrorLog 104, @UserId, 'sp_client_web_menus_byuserId', 'WebMenu', 'Get', 0, @UserId
		
		select a.menuId as menuid
				,isnull(a.[Path],'') as [path]
				,a.Title as title
				,case when a.isNavHeader = 0 then isnull(a.Icon,'fa ft-mail') else isnull(a.Icon,'') end as icon
				,isnull(a.Class,'') as classs
				,isnull(a.Badge,'') as badge
				,isnull(a.BadgeClass,'') as badgeClass
				,a.IsExternalLink as isExternalLink
				,a.isNavHeader as isNavHeader
				,null as action
				,a.intPos
				,b.menuRoleId
		from client_web_menu a 
			join client_role_menu b on a.menuId = b.menuId  --and a.webId = b.webRoleId
		where exists(select u.UserId FROM client_role_user u 
							join client_web_role wr on u.webRoleId = wr.webRoleId
							join client_webs w on wr.WebId = w.webId --
							join client_role_menu bb on u.WebRoleId = bb.WebRoleId
					where u.UserId = @UserId 
						--and wr.WebId = a.WebId 
						and u.work_st = 2
						and w.WebId = @WebId
						and bb.menuRoleId = b.menuRoleId
						)
				--and b.webRoleId = @webRoleId
				and (a.ParentId is null)

		order by a.[intPos]


		select   a.MenuId as menuid
				,isnull(a.[Path],'') as [path]
				,a.Title as title
				,case when a.isNavHeader = 0 then isnull(a.Icon,'fa ft-mail') else isnull(a.Icon,'') end as icon
				,isnull(a.Class,'') as classs
				,isnull(a.Badge,'') as badge
				,isnull(a.BadgeClass,'') as badgeClass
				,a.IsExternalLink as isExternalLink
				,a.isNavHeader as isNavHeader
				,isnull(a.parentId, a.ParentId) as parentid
				,null as action
				,a.intPos
				,b.menuRoleId
		from client_web_menu a 
			join client_role_menu b on a.menuId = b.menuId and a.parentId is not null --and a.webId = b.webId
		where exists(select u.UserId FROM client_role_user u 
							join client_web_role wr on u.webRoleId = wr.webRoleId
							join client_webs w on wr.WebId = w.webId 
							join client_role_menu bb on u.WebRoleId = bb.WebRoleId
					where u.UserId = @UserId 
						and w.WebId = a.WebId 
						and u.work_st = 2
						and u.WebId = @WebId
						--and bb.MenuId = a.MenuId
						and bb.menuRoleId = b.menuRoleId
						)
				--and b.webRoleId = @webRoleId
				and a.parentId is not null
		order by a.[intPos]


		SELECT b.[actionId]
			,b.[actionCd]
			,a.[menuId]
			,b.[actionName]
			,a.menuRoleId
		FROM client_role_action a 
			join client_web_action b on a.[actionId] = b.[actionId]
			join client_role_menu c on a.menuRoleId = c.menuRoleId
		WHERE b.webId = @WebId
			--and c.webRoleId = @webRoleId
			and (exists(select  u.UserId FROM client_role_user u 
							join client_webs w on u.WebId = w.webId 
					where u.UserId = @UserId 
						and u.WebId = b.WebId 
						and u.work_st = 2
						and u.WebRoleId = c.WebRoleId)
				--or (b.WebId = @WebId)
				)
		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Get_ClientMenu_ByUserId ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'WebMenu', 'Get', @SessionID, @AddlInfo
	end catch

GO

