CREATE TABLE [dbo].[customer_care] (
    [id]           UNI<PERSON><PERSON><PERSON>ENTIFIER NOT NULL,
    [customer_id]  UNIQUEIDENTIFIER NULL,
    [assistant_id] UNIQUEIDENTIFIER NULL,
    [created]      <PERSON><PERSON><PERSON><PERSON><PERSON>         NULL,
    [created_by]   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>TIFIER NULL,
    [updated]      <PERSON><PERSON><PERSON><PERSON><PERSON>         NULL,
    [updated_by]   UNIQUE<PERSON>ENTIFIER NULL,
    CONSTRAINT [PK_mas_customer_curator] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_customer_care_assistant] FOREIGN KEY ([assistant_id]) REFERENCES [dbo].[assistant] ([id]),
    CONSTRAINT [FK_customer_care_customer] FOREIGN KEY ([customer_id]) REFERENCES [dbo].[customer] ([id])
);


GO

