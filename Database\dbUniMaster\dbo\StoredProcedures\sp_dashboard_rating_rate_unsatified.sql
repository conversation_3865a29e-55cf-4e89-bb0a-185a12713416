
CREATE PROCEDURE [dbo].[sp_dashboard_rating_rate_unsatified]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(150) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @fromDate DATETIME = '2023-05-01',
    @toDate DATETIME = '2023-05-30',
    @area_Id UNIQUEIDENTIFIER = NULL -- '67DC0318-23CF-4F67-B1A8-D6AE2059EBEB'
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @filter = ISNULL(@filter, '');

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;
	-- Lấy service_id của userId
    DECLARE @service_id UNIQUEIDENTIFIER;
    SELECT @service_id = service_id
    FROM dbo.[user]
    WHERE userId = @userId

    SELECT Total = 1,
           totalFiltered = 1;
	--1. l<PERSON><PERSON> ds số vote theo tiêu chí đánh giá 
	SELECT sa.answer_VN AS rate_name,
           COUNT(sa.id) AS num_rate 
	FROM dbo.survey_answer sa
	LEFT JOIN dbo.survey_result sr  ON sa.id = sr.answer_id
	LEFT JOIN dbo.survey_question sq ON sq.id = sa.question_id
	LEFT JOIN dbo.survey_area t ON t.id = sr.area_id
	WHERE sq.question_type = 4
	AND (@area_Id IS NULL OR @area_Id = sr.area_id)
	AND (
                  CAST(sr.created AS DATE) >= CAST(@fromDate AS DATE)
                  And CAST(sr.created AS DATE) <= CAST(@toDate AS DATE)
              )
	AND sr.area_id IS NOT NULL
	AND sq.service_id = @service_id
	GROUP BY sa.answer_VN,sa.id
	


END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_dashboard_rating_rate_unsatified' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' @user: ' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'sp_dashboard_rating_rate_unsatified',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

