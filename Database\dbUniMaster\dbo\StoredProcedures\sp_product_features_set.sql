CREATE PROCEDURE [dbo].[sp_product_features_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @prod_id NVARCHAR(450) = NULL,
    @feature_name NVARCHAR(500) = NULL,
    @feature_desc NVARCHAR(500) = NULL,
    @icon NVARCHAR(500) = NULL,
    @image1 NVARCHAR(500) = NULL,
    @image2 NVARCHAR(500) = NULL,
    @image3 NVARCHAR(500) = NULL,
	@common_is bit
AS
begin
	DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);
BEGIN TRY

    

	if @common_is = 0 and not exists(select 1 from products where id = @prod_id)
	begin
		set @valid = 0
		set @messages = N'Bạn chưa chọn sản phẩm!'
		goto FINAL
	end

    IF (@id IS NULL)
    BEGIN
        SET @id = NEWID();
        -- insert
        INSERT INTO dbo.product_features
        (
            id,
            prod_id,
            feature_name,
            feature_desc,
            icon,
            image1,
            image2,
            image3,
            created,
            created_by,
			common_is
        )
        VALUES
        (   @id,                  -- id - uniqueidentifier
            @prod_id,             -- prod_id - uniqueidentifier
            @feature_name,        -- feature_name - nvarchar(50)
            @feature_desc, -- feature_desc - nvarchar(150)
            @icon,                -- icon - nvarchar(200)
            @image1,              -- image1 - nvarchar(200)
            @image2,              -- image2 - nvarchar(200)
            @image3,              -- image3 - nvarchar(200)
            GETDATE(),            -- created - datetime
            @userId,
			@common_is
			);
        --
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
        UPDATE dbo.product_features
        SET prod_id = @prod_id,
            feature_name = @feature_name,
            feature_desc = @feature_desc,
            icon = @icon,
            image1 = @image1,
            image2 = @image2,
            image3 = @image3,
            updated_by = @userId,
            updated = GETDATE(),
			common_is = @common_is
        WHERE id = @id;

        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_features_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;
	set @valid = 0
	set @messages = ERROR_MESSAGE()
    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_features',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH

SELECT @valid as valid
	,@messages AS [messages]

end

GO

