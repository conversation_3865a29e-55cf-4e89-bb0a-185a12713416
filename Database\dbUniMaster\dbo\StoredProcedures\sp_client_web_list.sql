


CREATE procedure [dbo].[sp_client_web_list]
	@UserId			nvarchar(50),
	@filter			nvarchar(100)

as
	begin try
			set @filter		= isnull(@filter,'')

			  select convert(nvarchar(50),WebId) as value
					,[WebKey]
					,[WebUrl]
					,[WebName] as name
					,[Description]
					,[IconUrl]
					,[Status]
					,[clientId]
					,[clientIdDev]
					,[mod_cd]
					,[isTab]
			   from client_webs a 
               where a.WebName like '%' + @filter + '%'
				and [Status] = 1
					--and (exists(select u.UserId 
					--		from client_web_role r 
					--		join client_role_user u on r.webRoleId = u.webRoleId 
					--		join client_web_user c on r.webId = c.webId and u.userId = c.userId 
					--where u.UserId = @UserId and r.WebId = a.WebId)
					--	or exists(select userid from client_users where userId = @userId and isSupper = 1)
					--	)
			   order by a.WebName 
				       

			   
	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Web_List ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'ClientWebs', 'Get', @SessionID, @AddlInfo
	end catch

GO

