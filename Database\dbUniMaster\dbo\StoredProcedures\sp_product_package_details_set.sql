CREATE PROCEDURE [dbo].[sp_product_package_details_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @package_id nvarchar(450) = NULL,
	@prod_id nvarchar(450) = NULL,
	@price DECIMAL = 0,
	@users_limit NVARCHAR(500) = NULL,
	@description nvarchar(500) = NULL,
	@package_price_type INT = 0
AS
BEGIN TRY

    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF (@id IS NULL)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.product_package_details
		(
		    id,
		    package_id,
		    prod_id,
		    price,
		    users_limit,
		    description,
		    package_price_type,
		    created_by,
		    created
		)
		VALUES
		(   @id,     -- id - uniqueidentifier
		    @package_id,     -- package_id - uniqueidentifier
		    @prod_id,     -- prod_id - uniqueidentifier
		    @price,     -- price - decimal(18, 0)
		    @users_limit,      -- users_limit - nvarchar(50)
		    @description,      -- description - nvarchar(250)
		    @package_price_type,        -- type - int
		    @userId,     -- created_by - uniqueidentifier
		    GETDATE() -- created_dt - datetime
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.product_package_details
		SET package_id = @package_id,
			prod_id = @prod_id,
			price = @price,
			users_limit = @users_limit,
			description = @description,
			[package_price_type] = @package_price_type,
			updated_by = @userId,
			updated = GETDATE()
        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_details_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package_details',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

