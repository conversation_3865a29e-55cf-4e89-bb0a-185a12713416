





CREATE procedure [dbo].[sp_user_role_del]
	 @manId		uniqueidentifier
	,@userId	uniqueidentifier
	,@webRoleId	uniqueidentifier
as
begin 
	declare @valid int = 1
	declare @messages nvarchar(500) =N'Xóa quyền thành công'

	begin try		
		--if exists (select 1 from client_role_user where userId = @userId 
		--	and webRoleId = @webRoleId
		--	and work_st = 2
		--	)
		--begin
		--	set @valid = 0
		--	set @messages = N'Nhóm quyền đã được sử dụng ! Không được phép xóa'
		--	goto FINAL
		--end

		DELETE FROM client_role_user
		WHERE userId = @userId 
			and webRoleId = @webRoleId
			and work_st < 2 or work_st = 3

		
	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_user_role_del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@User ' 

		exec utl_ErrorLog_set @ErrorNum, @ErrorMsg, @ErrorProc, 'User_Manage', 'Del', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as valid, @messages as [messages]

end

GO

