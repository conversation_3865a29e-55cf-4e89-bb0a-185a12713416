
CREATE PROCEDURE [dbo].[sp_order_list_get] 
@customer_id NVARCHAR(500) = NULL,
@filter NVARCHAR(500) = NULL
AS
BEGIN TRY
    --
    SELECT CONVERT(NVARCHAR(500), id) AS value,
            ord_no +'_' + ord_name AS name
    FROM dbo.[order]
    WHERE (
              @customer_id IS NULL
              OR cust_id = @customer_id
          )
		  AND 
		  ( 
			@filter IS NULL OR dbo.ufn_removeMark(ord_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		  )
    ORDER BY ord_name;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_list_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

