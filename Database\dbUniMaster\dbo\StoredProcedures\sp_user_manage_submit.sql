



CREATE procedure [dbo].[sp_user_manage_submit]
	@UserId			nvarchar(450),
	@submitUserId	uniqueidentifier,
	@remart			nvarchar(250),
	@reportTo		uniqueidentifier

as
begin
	declare @valid bit = 1
	declare @messages nvarchar(400) = N'Trình duyệt thành công'
	declare @tnx_object nvarchar(300)

	begin try	
		declare @work_type	nvarchar(50) = 'user_info' 
		declare @tran_ref_no nvarchar(100)
		if @UserId is null set @UserId = 'B721AA01-7AA2-4720-9A34-304C6D2EFA01'

		if not exists(select 1 from client_users where userId = @submitUserId)
		begin
			set @valid = 0
			set @messages = N'Không tìm thấy thông tin'
			goto FINAL
		end

		if not exists(select 1 from client_users
			where userId = @submitUserId 
			and (work_st = 0 or work_st = 3 or work_st is null))	
		begin
			set @valid = 0
			set @messages = N'Thông tin không hợp lệ'
			goto FINAL
		end
				

		select @tran_ref_no = cast(DATEDIFF(second,{d '1970-01-01'}, created_dt) as varchar(100))
			  ,@tnx_object = fullName 
		from client_users where userId = @submitUserId

		UPDATE [dbo].client_users 
		SET work_st = 1
		WHERE userId = @submitUserId 
			and (work_st = 0 or work_st = 3 or work_st is null)
			 
		EXECUTE [dbo].sp_work_flow_submit 
				 @UserId
				,@work_type
				,@submitUserId
				,@remart
				,@tran_ref_no
				,@tnx_object
				,@reportTo

				

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_uinv_scheme_submit ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserId ' + @UserId 
		set @valid = 0
		set @messages = error_message()
		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'WorkFlow', 'Set', @SessionID, @AddlInfo
	end catch


	FINAL:
	select @valid as [valid]
		  ,@messages as [messages]


end

GO

