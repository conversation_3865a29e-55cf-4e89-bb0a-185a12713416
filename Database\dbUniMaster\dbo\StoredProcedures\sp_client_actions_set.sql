
-- =============================================
-- Author:		<EMAIL>
-- Description: Set quyền vào menu
-- =============================================
CREATE procedure [dbo].[sp_client_actions_set]
	@userId nvarchar(450),
	@ActionId uniqueidentifier,
	@ActionCd nvarchar(350),
	@webId uniqueidentifier,
	@ActionName nvarchar(100)
as
	begin try
	if exists (select ActionId from [client_web_action] where ActionId  = @ActionId and webId = @webId)
		begin
			UPDATE [dbo].[client_web_action]
			   SET [actionCd] = @ActionCd
				  ,actionName = @ActionName
			 WHERE ActionId = @ActionId
				and webId = @webId
		end
	else
		begin
			set @ActionId =lower(newid())
			INSERT INTO [dbo].[client_web_action]
				   (ActionId
				   ,ActionCd
				   ,ActionName
				   ,webId
				   ,[created_dt])
			 VALUES
				   (@ActionId
				   ,@ActionCd
				   ,@ActionName
				   ,@webId
				   ,getdate()
				   )
		end

		select * 
		from client_web_action 
		where ActionId = @ActionId
			and webId = @webId

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_client_web_action_Set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'client_web_action', 'post', @SessionID, @AddlInfo
	end catch

GO

