



CREATE procedure [dbo].[sp_client_action_fields]
	@UserId		nvarchar(450),
	@actionId	uniqueidentifier,
	@webId		uniqueidentifier

as
	begin try
	    declare @gridWidth int
		declare @tableKey nvarchar(100) = 'client_web_action'

		set @gridWidth = isnull(@gridWidth,0)

		if exists(select actionId from client_web_action where actionId = @actionId and webId = @webId)
		begin
		--1--
			select actionId
				  ,webId 
				  ,@tableKey as tableKey
			  from client_web_action 
			  where actionId = @actionId
		--2--
			SELECT  objvalue as group_cd
					,objname as group_name 
			FROM [dbo].fn_config_data_gets ('common_group') 
	    --3--
			select [table_name]
				  ,[field_name]
				  ,[view_type]
				  ,[data_type]
				  ,[ordinal]
				  ,[columnLabel]
				  ,[group_cd]
				    ,isnull(case [data_type] 
					  when 'nvarchar' then convert(nvarchar(451), 
						case [field_name] 
							  when 'title' then b.actionCd
							  when 'path' then b.actionName
							  when 'webId' then cast(b.webId  as varchar(100))
						end) 
					  when 'datetime' then convert(nvarchar(100),
						case [field_name] 
							when 'created_dt' then format(b.created_dt,'dd/MM/yyyy HH:mm:ss')
						end ,103) 
					 -- else convert(nvarchar(50),
						--case [field_name] 							
						--	--when 'tabId' then b.tabId 
						--end) 
						end,[columnDefault])as columnValue
				  ,[columnClass]
				  ,[columnType]
				  ,[columnObject]
				  ,[isSpecial]
				  ,[isRequire]
				  ,[isDisable]
				  ,[IsVisiable]
				  ,isnull(a.columnTooltip,[columnLabel]) as columnTooltip
			  FROM sys_config_form a
				  ,client_web_action b  
			  where a.table_name = @tableKey
			  and (isVisiable = 1 or isRequire =1)
			  and b.actionId = @actionId
			  and b.webId = @webId
			  order by ordinal
		end
		else
			begin
			--4--
			select @webId as webId
				  ,@tableKey as tableKey

			--5--
			SELECT  objvalue as group_cd
					,objname as group_name 
			FROM [dbo].fn_config_data_gets ('common_group') 
			--6--
			select [table_name]
				  ,[field_name]
				  ,[view_type]
				  ,[data_type]
				  ,[ordinal]
				  ,[columnLabel]
				  ,[group_cd]
				  ,[columnDefault] as [columnValue]
				  ,[columnClass]
				  ,[columnType]
				  ,[columnObject]
				  ,[isSpecial]
				  ,[isRequire]
				  ,[isDisable]
				  ,[IsVisiable]
				  ,isnull(a.columnTooltip,[columnLabel]) as columnTooltip
			  from (select * from sys_config_form
					where table_name = @tableKey
						and (isVisiable = 1 or isRequire =1)) as a
			  order by a.ordinal
			end

			

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Menu_Fields ' + error_message()
		set @ErrorProc					= error_procedure()
		set @AddlInfo					= ' '

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'WebMenu', 'GET', @SessionID, @AddlInfo
	
	end catch

GO

