
CREATE PROCEDURE [dbo].[sp_customer_list_get] @filter NVARCHAR(500) = NULL
AS
BEGIN TRY
    --
    SELECT CONVERT(NVARCHAR(500), id) AS value,
           full_name AS name
    FROM dbo.customer
    WHERE (
              @filter IS NULL
              OR (dbo.ufn_removeMark(full_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
          )
    ORDER BY full_name;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_list_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'customer',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

