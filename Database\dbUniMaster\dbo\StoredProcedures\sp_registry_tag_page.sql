-- =============================================
-- Author: AnhTT
-- Create date: 2025-04-09
-- Description:	Danh sách tag
-- Output: 
-- =============================================
CREATE PROCEDURE dbo.sp_registry_tag_page @userId NVARCHAR(50) = NULL,
    @componentId UNIQUEIDENTIFIER,
    @filter NVARCHAR(100) = NULL,
    @gridWidth INT = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @gridKey NVARCHAR(50) OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    --
    IF @PageSize = 0
        SET @PageSize = 10;

    IF @Offset < 0
        SET @Offset = 0;

    SELECT @total = COUNT(1)
    FROM registry_tag t
    INNER JOIN registry_artifact ra ON ra.id = t.artifact_id
    INNER JOIN registry_repository r ON r.id = ra.repository_id

    SET @TotalFiltered = @Total;

    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets('view_registry_tag_page', 0)
        ORDER BY [ordinal];
    END;

    -- Data
    SELECT *
    FROM registry_tag t
    INNER JOIN registry_artifact ra ON ra.id = t.artifact_id
    INNER JOIN registry_repository r ON r.id = ra.repository_id
    WHERE (
            t.name LIKE '%' + @filter + '%'
            OR r.name LIKE '%' + @filter + '%'
            )
    ORDER BY t.push_time DESC OFFSET @Offset ROWS

    FETCH NEXT @PageSize ROWS ONLY;
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT,
        @ErrorMsg VARCHAR(200),
        @ErrorProc VARCHAR(50),
        @SessionID INT,
        @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_line_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
        @ErrorMsg,
        @ErrorProc,
        'product',
        'GET',
        @SessionID,
        @AddlInfo;
END CATCH;

GO

