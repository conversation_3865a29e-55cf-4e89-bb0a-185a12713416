

-- =============================================
-- Author:		<EMAIL>
-- Description: Thêm menu cho web
-- =============================================
CREATE procedure [dbo].[sp_client_menu_set]
	@userId nvarchar(450),
	@MenuId uniqueidentifier,
	@WebId uniqueidentifier,
	@Title nvarchar(100),
	@Path nvarchar(200),
	@Icon nvarchar(50),
	@Class nvarchar(100),
	@Badge nvarchar(100),
	@BadgeClass nvarchar(100),
	@IsExternalLink bit,
	@IsNavHeader bit,
	@parentId uniqueidentifier,
	--@TabId int,
	@IntPos int,
	@actionIds nvarchar(max)
as
begin 
	declare @valid bit = 1
	declare @messages nvarchar(300)

	begin try
	BEGIN TRAN Client_Menu_Set_transtion
		declare @tbAc TABLE 
		(
			actionId uniqueidentifier null,
			intOrd int
		)
		if not exists(select 1 from client_webs where webid = @WebId)
		begin 
			set @valid = 0
			set @messages = N'Không tìm thấy thông tin WEB!'
			goto FINAL
		end
		if exists (select MenuId from [client_web_menu] where MenuId = @MenuId and webId = @WebId)
		begin
			UPDATE [dbo].[client_web_menu]
			SET	   [WebId] = @WebId				  
				  ,[Title] = @Title
				  ,[Path] = @Path
				  ,[Icon] = @Icon
				  ,[Class] = @Class
				  ,[Badge] = @Badge
				  ,[BadgeClass] = @BadgeClass
				  ,[isExternalLink] = @IsExternalLink
				  ,[isNavHeader] = @IsNavHeader
				  ,[ParentId] = @ParentId
				  ,[intPos] = @IntPos
				  ,created_dt = getdate()
			where MenuId = @MenuId
				and webId = @WebId
		set @messages = N'Cập nhật Menu thành công'
		end
	else
		begin
			set @MenuId = newid()
			INSERT INTO [dbo].[client_web_menu]
					   ([WebId]
					   ,[Title]
					   ,[Path]
					   ,[Icon]
					   ,[Class]
					   ,[Badge]
					   ,[BadgeClass]
					   ,[isExternalLink]
					   ,[isNavHeader]
					   ,[ParentId]					   
					   ,[intPos]
					   ,MenuId
					   ,created_dt)
          VALUES
					   (@WebId
					   ,@Title
					   ,@Path
					   ,@Icon
					   ,@Class
					   ,@Badge
					   ,@BadgeClass
					   ,@IsExternalLink
					   ,@IsNavHeader
					   ,@ParentId
					   ,@IntPos
					   ,@MenuId
					   ,getdate())
			
			set @messages = N'Thêm mới Menu thành công'
		end
		
		if @actionIds is not null and @actionIds <> ''
			INSERT INTO @tbAc SELECT cast(part as uniqueidentifier), id FROM [dbo].fn_split_string (@actionIds,',')
			
		delete t from [client_menu_action] t 
			where t.menuId = @MenuId 
				and (t.actionId not in (SELECT actionId FROM @tbAc))
				and exists(select 1 from client_web_menu where webId = @webId and menuId = t.menuId)
		UPDATE t
		   SET [intOrd] = a.intOrd
		FROM [dbo].[client_menu_action] t
			join @tbAc a on t.actionId = a.actionId and t.menuId = t.menuId
		 WHERE t.menuId = @menuId
			and exists(select 1 from client_web_menu where webId = @webId and menuId = t.menuId)

		INSERT INTO [dbo].[client_menu_action]
			   ([actionId]
			   ,[menuId]
			   ,created_dt
			   ,intOrd
			   )
		 SELECT t.actionId
		       ,@menuId
			   ,getdate()
			   ,intOrd
			 FROM @tbAc t
			   where not exists(select 1 from [client_menu_action] ma
					join client_web_menu wm on ma.menuId = wm.menuId 
					where ma.menuId = @menuId 
						and ma.[actionId] = t.actionId
						and wm.webId = @WebId)

					
	COMMIT TRAN Client_Menu_Set_transtion
	end try
	begin catch
	ROLLBACK TRAN Client_Menu_Set_transtion

		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Menu_Set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''
		set @valid = 0
		set @messages =  error_message()

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'sp_Client_', 'Get', @SessionID, @AddlInfo
	end catch

	FINAL:

	select @valid as valid
	      ,@messages as [messages]
		  ,@MenuId as id
end

GO

