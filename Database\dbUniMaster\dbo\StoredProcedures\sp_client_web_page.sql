

CREATE procedure [dbo].[sp_client_web_page]
	@UserId			nvarchar(50),
	@filter			nvarchar(100),
	@gridWidth		int		= 0,
	@Offset			int				= 0,
	@PageSize		int				= 50,
	@Total			int out,
	@TotalFiltered	int out,
	@gridKey		nvarchar(100) out

as
	begin try
        --BEGIN
        --    Update t
        --    set t.last_dt = getdate()
        --    from client_users t
        --    where userId = @UserId
        --END
		set		@Offset					= isnull(@Offset, 0)
		set		@PageSize				= isnull(@PageSize, 50)
		set		@Total					= isnull(@Total, 0)
		set		@filter					= isnull(@filter,'')

		if		@PageSize	= 0			set @PageSize	= 10
		if		@Offset		< 0			set @Offset		=  0

		set		@gridKey				= 'view_Client_Web_Page'

		select @Total	= count(a.webId)
				from Client_Webs a
				where a.WebName like '%' + @filter + '%'
					and [Status] = 1

				set @TotalFiltered = @Total

				if @Offset = 0
				begin
					select * from [dbo].fn_config_list_gets (@gridKey, @gridWidth) 
					order by [ordinal]
				end
					
				select   [webId]
						,[webKey]
						,[webUrl]
						,[webName]
						,[description]
						,[iconUrl]
						,created_by
						,[Status]
						,[clientId]
						,[clientIdDev]
						,[mod_cd]
						,[isTab]
						,created_by
						,created_dt
						,(select count(m.menuId) from Client_Web_Menu m where m.webId = a.webId) as menuCount
						,(select count(r.webRoleId) from Client_Web_Role r where r.webId = a.webId) as roleCount
				from client_webs a 
               where a.webName like '%' + @filter + '%'
					and [Status] = 1
			   order by a.[webName] 
				   offset @Offset rows fetch next @PageSize rows only

			   

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Web_Page ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'ClientWebs,ClientWebRoles', 'Get', @SessionID, @AddlInfo
	end catch

GO

