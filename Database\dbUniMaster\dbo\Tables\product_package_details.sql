CREATE TABLE [dbo].[product_package_details] (
    [id]                 UNIQUEIDENTIFIER CONSTRAINT [DF_product_quotation_details_id] DEFAULT (newid()) NOT NULL,
    [package_id]         UNIQUEIDENTIFIER NULL,
    [prod_id]            UNIQUEIDENTIFIER NULL,
    [price]              DECIMAL (18)     NULL,
    [users_limit]        NVARCHAR (50)    NULL,
    [user_limit_from]    INT              NULL,
    [user_limit_to]      INT              NULL,
    [description]        NVARCHAR (250)   NULL,
    [package_price_type] INT              CONSTRAINT [DF_product_package_details_type] DEFAULT ((0)) NULL,
    [start_dt]           DATETIME         NULL,
    [end_dt]             DATETIME         NULL,
    [created]            DATETIME         NULL,
    [created_by]         UNIQUEIDENTIFIER NULL,
    [updated]            DATETIME         NULL,
    [updated_by]         UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_prod_quotation_details] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_product_package_details_product_package] FOREIGN KEY ([package_id]) REFERENCES [dbo].[product_package] ([id]),
    CONSTRAINT [FK_product_package_details_products] FOREIGN KEY ([prod_id]) REFERENCES [dbo].[products] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'0 : Theo tháng, 1. Theo năm', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_package_details', @level2type = N'COLUMN', @level2name = N'package_price_type';


GO

