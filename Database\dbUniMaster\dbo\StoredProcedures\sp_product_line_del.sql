CREATE PROCEDURE [dbo].[sp_product_line_del]
	@userId nvarchar(50)
	,@id uniqueidentifier
AS
BEGIN TRY
	declare @valid bit = 0, @messages nvarchar(250)

	IF NOT EXISTS(SELECT 1 FROM dbo.product_line WHERE Id = @id)
	BEGIN
		SET @messages = N'Bản ghi không tồn tại'
		GOTO FINAL
	END

	IF EXISTS(SELECT TOP 1 1 FROM dbo.products WHERE prod_line_id = @id)
	BEGIN
		SET @messages = N'Tồn tại sản phẩm trong dòng sản phẩm này. Không thể xoá'
		GOTO FINAL
	END
	
	DELETE FROM dbo.product_line WHERE id = @id
	--
	SET @valid = 1
	SET @messages = N'Xóa thành công'
	--
	FINAL:
		SELECT @valid valid, @messages as [messages]
END TRY
BEGIN CATCH
	SELECT @messages AS [messages]
	DECLARE	@ErrorNum				int,
			@ErrorMsg				varchar(200),
			@ErrorProc				varchar(50),

			@SessionID				int,
			@AddlInfo				varchar(max)

	set @ErrorNum					= error_number()
	set @ErrorMsg					= 'sp_product_line_del' + error_message()
	set @ErrorProc					= error_procedure()

	set @AddlInfo					= '@Userid'  + @UserId

	exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'product_line', 'DEL', @SessionID, @AddlInfo
end catch

GO

