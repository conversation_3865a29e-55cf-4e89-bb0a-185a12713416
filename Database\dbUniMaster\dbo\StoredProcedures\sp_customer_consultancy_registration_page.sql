
-- =============================================
-- Author: AnhTT
-- Create date: 2025-04-23
-- Description:	<PERSON>h sách khách hàng đăng ký tư vấn
-- Output: 
-- =============================================
CREATE PROCEDURE [dbo].[sp_customer_consultancy_registration_page] @userId NVARCHAR(50) = NULL
    , @filter NVARCHAR(100) = NULL
    , @gridWidth INT = NULL
    , @Offset INT = 0
    , @PageSize INT = 10
    , @Total INT = 0 OUT
    , @gridKey NVARCHAR(50) OUT
    , @TotalFiltered INT = 0 OUT
AS
BEGIN TRY
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    --
    IF @PageSize = 0
        SET @PageSize = 10;

    IF @Offset < 0
        SET @Offset = 0;

    SELECT @total = COUNT(1)
    FROM customer_consultancy_registration t

    SET @TotalFiltered = @Total;

    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets('view_customer_consultancy_registration_page', 0)
        ORDER BY [ordinal];
    END;

    -- Data
    SELECT [id]
        , [Fullname]
        , [Phone]
        , [Email]
        , [Position]
        , [CompanyName]
        , [Region]
        , [CompanyAddress]
        , [CompanySize]
        , [Product]
        , [JsonData]
        , [refId]
        , [documentId]
        , [CreatedDate]
        , [UpdatedDate]
    FROM customer_consultancy_registration
    ORDER BY CreatedDate DESC OFFSET @Offset ROWS

    FETCH NEXT @PageSize ROWS ONLY;
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_consultancy_registration_page ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'customer_consultancy_registration'
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

