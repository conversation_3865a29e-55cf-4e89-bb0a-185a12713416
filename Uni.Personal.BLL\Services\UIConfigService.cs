using System;
using System.Threading.Tasks;
using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// UIConfig service implementation
    /// </summary>
    public class UIConfigService : IUIConfigService
    {
        private readonly IUIConfigRepository _uiConfigRepository;

        public UIConfigService(IUIConfigRepository uiConfigRepository)
        {
            _uiConfigRepository = uiConfigRepository;
        }

        /// <summary>
        /// Get paginated list of UI configurations
        /// </summary>
        /// <param name="query">Filter input for UI configurations</param>
        /// <returns>Paginated list of UI configurations</returns>
        public async Task<CommonListPage> GetPageAsync(UIConfigFilterInput? query)
        {
            return await _uiConfigRepository.GetPageAsync(query);
        }

        /// <summary>
        /// Get UI configuration information by ID
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>UI configuration information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            return await _uiConfigRepository.GetInfoAsync(oid);
        }

        /// <summary>
        /// Create or update UI configuration
        /// </summary>
        /// <param name="info">UI configuration information</param>
        /// <returns>Validation result with UI configuration ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            return await _uiConfigRepository.SetInfoAsync(info);
        }

        /// <summary>
        /// Delete UI configuration
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            // Add business logic validation here if needed
            // For example: check if UI configuration can be deleted, validate permissions, etc.

            return await _uiConfigRepository.DeleteAsync(oid);
        }
    }
}
