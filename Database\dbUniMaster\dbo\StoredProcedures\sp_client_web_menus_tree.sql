

-- =============================================
-- Author:		<EMAIL>
-- Description:	l<PERSON>y thông tin menu theo người dùng
-- =============================================
-- exec sp_Client_Web_Menus_ByUserId '2dc6ff82-5442-4bec-9af1-c73f52916e4d','6F84B537-05F2-4B11-A891-67B4EF1847DC'
CREATE procedure [dbo].[sp_client_web_menus_tree]
	@UserId nvarchar(450) = null,
	@WebId nvarchar(50) = '70e930b0-ffea-43d3-b3a9-0e6b03f2b433'
as
	begin try
		if @UserId is null set @UserId = '81739c5c-2ca0-4e0f-acab-63373ea8a34a'
		
			  select a.[MenuId] as [MenuId]
					,a.[WebId]  as [WebId]
					,[Title]
					,[Path]
					,[Icon]
					,[Class]
					,[Badge]
					,[BadgeClass]
					,a.[isExternalLink]
					,a.[isNavHeader]
					,case when exists(select 1 from [Client_Web_Menu] where webId = @webId and menuId = a.parentId) then [parentId] else null end as [parentId]
					,a.[intPos] as IntPos
					,childCount = (select count(*) from [Client_Web_Menu] where parentId = a.menuId)
		from client_web_menu a 
			--join client_role_menu b on a.MenuId = b.MenuId and (a.ParentId is null) --and a.webId = b.webRoleId
		where a.WebId = @webId
			--AND exists(select u.UserId FROM client_role_user u 
			--				join client_webs w on u.WebId = w.webId and u.WebId = @WebId
			--		where u.UserId = @UserId 
			--			and u.WebId = a.WebId 
			--			and u.WebRoleId = b.WebRoleId)
		order by a.[intPos]

		  SELECT b.[actionId]
				,b.[actionCd]
				,a.[menuId]
				,b.[actionName]
				,b.created_dt
			FROM [Client_Menu_Action] a 
			join client_web_action b on a.[actionId] = b.[actionId]
			WHERE b.webId = @webId
			order by a.[menuId], a.intOrd 
		
		--select   a.MenuId as menuid
		--		,isnull(a.[Path],'') as [path]
		--		,a.Title as title
		--		,case when b.isNavHeader = 0 then isnull(a.Icon,'fa ft-mail') else isnull(a.Icon,'') end as icon
		--		,isnull(a.Class,'') as classs
		--		,isnull(a.Badge,'') as badge
		--		,isnull(a.BadgeClass,'') as badgeClass
		--		,a.IsExternalLink as isExternalLink
		--		,a.isNavHeader as isNavHeader
		--		,isnull(b.parentId, a.ParentId) as parentid
		--		,null as action
		--		--,b.IsAdd as isAdd
		--		--,b.IsEdit as isEdit
		--		--,b.IsDelete as isDelete
		--		,b.intPos
		--		--,b.[action]
		--		,b.menuRoleId
		--from client_web_menu a 
		--	join client_role_menu b on a.MenuId = b.MenuId and a.parentId is not null --and a.webId = b.webId
		--where exists(select u.UserId FROM client_role_user u 
		--					join client_webs w on u.WebId = w.webId and u.WebId = @WebId
		--			where u.UserId = @UserId 
		--				and u.WebId = a.WebId 
		--				and u.WebRoleId = b.WebRoleId)
		--order by b.[intPos]


		--SELECT b.[actionId]
		--	,b.[actionCd]
		--	,a.[menuId]
		--	,b.[actionName]
		--	,a.menuRoleId
		--FROM client_role_action a 
		--	join client_web_action b on a.[actionId] = b.[actionId]
		--	join client_role_menu c on a.menuRoleId = c.menuRoleId
		--WHERE b.webId = @WebId
		--	and (exists(select  u.UserId FROM client_role_user u 
		--					join client_webs w on u.WebId = w.webId and u.WebId = @WebId
		--			where u.UserId = @UserId 
		--				and u.WebId = b.WebId 
		--				and u.WebRoleId = c.WebRoleId)
		--		or (b.WebId = @WebId)
		--		)
		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_client_web_menus_tree ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'WebMenu', 'Get', @SessionID, @AddlInfo
	end catch

GO

