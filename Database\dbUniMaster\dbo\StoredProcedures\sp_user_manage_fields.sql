









CREATE procedure [dbo].[sp_user_manage_fields]
	@reportId	nvarchar(450),
	@action		nvarchar(10) = 'view',
	@userId		uniqueidentifier,
	@webId		uniqueidentifier = null

as
	begin try
		declare @tableKey nvarchar(100) = 'client_users'
		--1 user
		--if @UserId is not null and not exists(select 1 from client_users where UserId = @UserId) set @UserId = null 

		select @UserId as gd
			  ,@tableKey as tableKey

		--2 group
			SELECT  *
			FROM [dbo].fn_get_field_group('user_role_group') 
		   order by intOrder
		
		--3 field
		SELECT [table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			  ,isnull(case [data_type] 
				  when 'nvarchar' then convert(nvarchar(350), 
					case [field_name] 
						when 'avatarUrl' then b.avatarUrl 
						when 'loginName' then b.loginName
						when 'fullName' then b.fullName 
						when 'phone' then b.phone
						when 'email' then b.email
						when 'position' then b.position
						when 'userId' then cast(b.userId as varchar(50))
						when 'auth1_by' then au1.fullName--cast(b.auth1_by as varchar(50))
						when 'auth2_by' then au2.fullName--cast(b.auth2_by as varchar(50))
                        when 'created_by' then ct.fullName

					end) 
				  when 'datetime' then 
					case [field_name] 
						when 'auth1_dt' then format(b.auth1_dt ,'dd/MM/yyyy HH:mm:ss')
						when 'auth2_dt' then format(b.auth2_dt ,'dd/MM/yyyy HH:mm:ss')
						when 'created_Dt' then format(b.created_Dt ,'dd/MM/yyyy HH:mm:ss')
					end 
				  else convert(nvarchar(50),
					case [field_name] 
						when 'isSupper' then b.isSupper
					    when 'auth1_st' then b.auth1_st  
						when 'auth2_st' then b.auth2_st
						when 'lock_st' then b.lock_st 
						when 'userRoleType' then b.userRoleType
						when 'work_st' then b.work_st
						when 'auth1_st' then b.auth1_st
						when 'auth2_st' then b.auth2_st
                     
					end) end,[columnDefault])as columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject]
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,case when @action = 'edit' then 1 else 0 end as isChange
		  FROM (select * from sys_config_form a
			where table_name = @tableKey
				and (isvisiable = 1 or isRequire = 1)
				and view_type = 0) a
			left join client_users b on b.UserId = @UserId
			left join client_users au1 on b.auth1_by = au1.userId
			left join client_users au2 on b.auth2_by = au2.userId
            left join client_users ct on b.created_by = ct.userId
		  order by ordinal

		  	--6 role
			SELECT a.WebRoleId
				  ,a.WebId
				  ,a.UserId
				  ,b.isAdmin 
				  ,b.RoleName 
				  ,c.webName
			FROM client_role_user a
				join client_web_role b on a.webRoleId = b.webRoleId
				join client_webs c on a.webId = c.webId
			WHERE a.UserId = @UserId 
				--and a.webId = @webId

			

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_Fields ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'Manage_Fields', 'GET', @SessionID, @AddlInfo
	end catch

GO

