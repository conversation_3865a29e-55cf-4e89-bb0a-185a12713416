














CREATE procedure [dbo].[sp_work_flow_approve]
	@UserId		nvarchar(450),
	@wft_type	nvarchar(50),
	@wft_id		uniqueidentifier,
	@wft_st		int,
	@tnx_reason nvarchar(100)
	--@approved	int out

as
begin
	declare @valid bit = 1
	declare @messages nvarchar(400) = ''

	begin try	
	
	declare @custId nvarchar(100)
	declare @cif_no nvarchar(20)
	declare @tnx_userid nvarchar(450)
	declare @mkr_id nvarchar(100)
	--set @UserId = 'D58B7992-5024-4BE8-9450-012371971E6B'

		if @wft_type <> 'user_info' and @wft_type <> 'user_role'
		Begin
			set @valid = 0
			set @messages = N'Không áp dụng mã ' + @wft_type
			goto FINAL
		end

		if not (@wft_st = 1 or @wft_st = 2)
		Begin
			set @valid = 0
			set @messages = N'Trạng thái không hợp lệ'
			goto FINAL
		end

		if not exists (select wft_id from work_flow_tb where wft_id = @wft_id 
				and wft_type = @wft_type)
		Begin
			set @valid = 0
			set @messages = N'Không tìm thấy thông tin phê duyệt'
			goto FINAL
		end

		if @wft_type = 'user_info'
		begin
			-- if exists(select 1 FROM client_users a 
			--		join work_flow_tb b on a.userId = b.tnx_id 
			--		where wft_id = @wft_id 
			--			and auth1_st = 1
			--			and a.auth1_by = @userId)
			--begin
			--	set @valid = 0
			--	set @messages = N'Bạn đã người phê duyệt thứ nhất, yêu cầu người duyệt thứ 2'
			--	goto FINAL
			--end
			--else 
            if exists(select 1 FROM client_users a 
					join work_flow_tb b on a.userId = b.tnx_id 
					where wft_id = @wft_id 
						and (auth1_st is null or auth1_st = 0)
						and a.work_st = 1)
			begin
				if @wft_st = 1
                    begin
					    UPDATE a
					       SET work_st = 2 
                              ,auth1_st = 1
						      ,auth1_by = @UserId
						      ,auth1_dt = getdate()
					    FROM client_users a 
						    join work_flow_tb b on a.userId = b.tnx_id 
						    where wft_id = @wft_id 
							    and a.work_st = 1

                          UPDATE [dbo].work_flow_tb
					       SET wft_st = @wft_st
						      ,tnx_reason = @tnx_reason
						      ,auth_id = @UserId
						      ,auth_dt = getdate()
					     WHERE wft_id = @wft_id 
						    and wft_type = @wft_type
         --               UPDATE a
					    --   SET work_st = 2
						   --   ,auth2_st = 1
						   --   ,auth2_by = @UserId
						   --   ,auth2_dt = getdate()
					    --FROM client_users a 
						   -- join work_flow_tb b on a.userId = b.tnx_id 
						   -- where wft_id = @wft_id --and work_type = @work_type
							  --  and a.work_st = 1
                    end
				else
				    begin
					    UPDATE a
					       SET work_st = 3
						      ,auth1_by = @UserId
						      ,auth1_dt = getdate()
					    FROM client_users a 
						    join work_flow_tb b on a.userId = b.tnx_id 
						    where wft_id = @wft_id 
							    and a.work_st = 1
					
					    UPDATE [dbo].work_flow_tb
					       SET wft_st = @wft_st
						      ,tnx_reason = @tnx_reason
						      ,auth_id = @UserId
						      ,auth_dt = getdate()
					     WHERE wft_id = @wft_id 
						    and wft_type = @wft_type

				    end
			end
			--else
			--begin
			--	if @wft_st = 1
			--	begin
			--		 UPDATE [dbo].work_flow_tb
			--		   SET wft_st = @wft_st
			--			  ,tnx_reason = @tnx_reason
			--			  ,auth_id = @UserId
			--			  ,auth_dt = getdate()
			--		 WHERE wft_id = @wft_id 
			--			and wft_type = @wft_type

			--		--Update User Info
			--		UPDATE a
			--		   SET work_st = 2
			--			  ,auth2_st = 1
			--			  ,auth2_by = @UserId
			--			  ,auth2_dt = getdate()
			--		FROM client_users a 
			--			join work_flow_tb b on a.userId = b.tnx_id 
			--			where wft_id = @wft_id --and work_type = @work_type
			--				and a.work_st = 1
			--	end
			--	else
			--	begin
			--		 UPDATE [dbo].work_flow_tb
			--		   SET wft_st = @wft_st
			--			  ,tnx_reason = @tnx_reason
			--			  ,auth_id = @userId
			--			  ,auth_dt = getdate()
			--		 WHERE wft_id = @wft_id 
			--			and wft_type = @wft_type

			--		--Update User Info
			--		UPDATE a
			--		   SET work_st = 3
			--			  ,auth2_st = 0
			--			  ,auth2_by = @UserId
			--			  ,auth2_dt = getdate()
			--		FROM client_users a 
			--			join work_flow_tb b on a.userId = b.tnx_id 
			--			where wft_id = @wft_id --and work_type = @work_type
			--				and a.work_st = 1
			--	end

			--end
		end	
		else if @wft_type = 'user_role'
		begin
			 
				 UPDATE [dbo].work_flow_tb
				   SET wft_st = @wft_st
					  ,tnx_reason = @tnx_reason
					  ,auth_id = @UserId
				 WHERE wft_id = @wft_id 
					and wft_type = @wft_type

				if @wft_st = 1
					UPDATE a
					   SET work_st = 2
						  ,auth_by = @UserId
						  ,auth_dt = getdate()
					FROM client_role_user a 
						join work_flow_tb b on a.userRoleId = b.tnx_id 
						where wft_id = @wft_id 
							and wft_type = @wft_type
							and a.work_st = 1
				else
					UPDATE a
					   SET work_st = 3
						  ,auth_by = @UserId
						  ,auth_dt = getdate()
					FROM client_role_user a 
						join work_flow_tb b on a.userRoleId = b.tnx_id 
						where wft_id = @wft_id 
							and wft_type = @wft_type
							and a.work_st = 1

				set @messages = N'Duyệt thành công!'
			
		end
			

	--COMMIT TRAN COR_Workflow_Approve_trans
	end try
	begin catch
	--ROLLBACK TRAN COR_Workflow_Approve_trans

		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_WorkFlow_Approve ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserId ' + @UserId 
		set @valid = 0
		set @messages = error_message()

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'CORFlow', 'Set', @SessionID, @AddlInfo
	end catch


	FINAL:
	--set @approved = @valid
	select @valid as [valid]
		  ,@messages as [messages]
	
	if @wft_type = 'user_role' --and @wft_st = 1		
		select u.userId
			  ,u.fullName
			  ,u.loginName as userName
			  ,a.channel
			  ,w.mod_cd as prod_type
			  ,active = case when a.work_st = 2 then 1 else 0 end
              ,cast(a.orgId as nvarchar(50)) as orgId
              ,cast(a.companyId as nvarchar(50)) as compaynies
		FROM client_role_user a 
			join work_flow_tb b on a.userRoleId = b.tnx_id 
			join client_users u on a.userId = u.userId
			join client_web_role r on a.webRoleId = r.webRoleId
			join client_webs w on r.webId = w.webId
			where wft_id = @wft_id 
				and wft_type = @wft_type
				--and a.work_st = 2
	else
		select null

	end

GO

