


-- =============================================
-- Author:		<EMAIL>
-- Description: Thêm menu cho web
-- =============================================
CREATE procedure [dbo].[sp_client_menu_del]
	@userId nvarchar(450),
	@menuId uniqueidentifier,
	@webId	uniqueidentifier

as
	begin try
		begin transaction Deletemenu_tran
	    declare @valid int = 0
		declare @messages nvarchar(500) =N'Xóa menu thành công'
		-- xóa toàn bộ một nhóm quyền
			begin
				if exists (select menuId from client_role_menu a
						where menuId = @menuId
					and exists(select 1 from client_web_menu where webId = @webId and menuId = a.menuId))
					begin
						set @messages = N'Nhóm quyền đã được sử dụng ! <PERSON>hông được phép xóa'
					end
				else
					begin

						delete r from client_role_menu r 
							where menuId = @menuId 
								and exists(select 1 from client_web_role where webRoleId = r.webRoleId and webId = @webId)
								and exists(select 1 from client_web_menu where menuId = r.menuId and webId = @webId)

						Delete m from client_menu_action m 
							where menuId = @menuId 
								and exists(select 1 from client_web_menu where menuId = m.menuId and webId = @webId)

						Delete from client_web_menu where menuId = @menuId and webId = @webId

						set @valid = 1
					end	
			end
		
		select @valid as valid, @messages as messages
 
	COMMIT TRAN Deletemenu_tran
	end try
	begin catch
	ROLLBACK TRAN Deletemenu_tran

		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Menu_Del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'sp_Client_Menu_Del', 'del', @SessionID, @AddlInfo
	end catch

GO

