CREATE TABLE [dbo].[deployment_product_component] (
    [id]            UNIQ<PERSON>IDENTIFIER CONSTRAINT [DF__tmp_ms_xx_de__id__5B2E79DB] DEFAULT (newid()) NOT NULL,
    [module_id]     UN<PERSON><PERSON><PERSON><PERSON>EN<PERSON>FIER NULL,
    [product_id]    UNI<PERSON><PERSON><PERSON>ENTIFIER NULL,
    [code]          <PERSON><PERSON><PERSON><PERSON><PERSON> (50)    NULL,
    [name]          NVARCHAR (250)   NULL,
    [repository_id] BIGINT           NULL,
    [created]       DATETIME         CONSTRAINT [DF__tmp_ms_xx__creat__5C229E14] DEFAULT (getdate()) NULL,
    [created_by]    UNI<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ER NULL,
    [updated]       D<PERSON><PERSON>IME         NULL,
    [updated_by]    UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK__tmp_ms_x__3213E83FBC28933A] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_deployment_product_component_product_module] FOREIGN KEY ([module_id]) REFERENCES [dbo].[product_module] ([id]),
    CONSTRAINT [FK_deployment_product_component_registry_repository] FOREIGN KEY ([repository_id]) REFERENCES [dbo].[registry_repository] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Các thành phần của sp cần deploy (vd: api, web(fe),...)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'deployment_product_component';


GO

