-- =============================================
-- Order Management Database Tables
-- =============================================

-- =============================================
-- Table: Orders
-- Description: Main order table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Orders' AND xtype='U')
BEGIN
    CREATE TABLE Orders (
        Oid UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        OrderCode NVARCHAR(50) NOT NULL UNIQUE,
        ContractCode NVARCHAR(50) NULL,
        CustomerName NVARCHAR(255) NOT NULL,
        CustomerEmail NVARCHAR(255) NULL,
        CustomerPhone NVARCHAR(50) NULL,
        SubTotal DECIMAL(18,2) NOT NULL DEFAULT 0,
        VatRate DECIMAL(5,2) NOT NULL DEFAULT 10,
        VatAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        TotalAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        Status INT NOT NULL DEFAULT 0, -- 0: Draft, 1: WaitingForPayment, 2: Paid, 3: Processing, 4: Completed, 5: Cancelled, 6: Refunded
        OrderDate DATETIME NOT NULL DEFAULT GETDATE(),
        Description NVARCHAR(MAX) NULL,
        Notes NVARCHAR(MAX) NULL,
        CreatedBy NVARCHAR(255) NULL,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ModifiedBy NVARCHAR(255) NULL,
        ModifiedDate DATETIME NULL
    );
    
    -- Create indexes
    CREATE INDEX IX_Orders_OrderCode ON Orders(OrderCode);
    CREATE INDEX IX_Orders_ContractCode ON Orders(ContractCode);
    CREATE INDEX IX_Orders_CustomerName ON Orders(CustomerName);
    CREATE INDEX IX_Orders_Status ON Orders(Status);
    CREATE INDEX IX_Orders_OrderDate ON Orders(OrderDate);
    CREATE INDEX IX_Orders_CreatedDate ON Orders(CreatedDate);
END
GO

-- =============================================
-- Table: OrderItems
-- Description: Order line items table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderItems' AND xtype='U')
BEGIN
    CREATE TABLE OrderItems (
        Oid UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        OrderOid UNIQUEIDENTIFIER NOT NULL,
        ItemNo INT NOT NULL,
        ProductType NVARCHAR(100) NULL,
        ProductName NVARCHAR(255) NOT NULL,
        ProductDescription NVARCHAR(MAX) NULL,
        Quantity INT NOT NULL DEFAULT 1,
        Unit NVARCHAR(50) NULL,
        UnitPrice DECIMAL(18,2) NOT NULL DEFAULT 0,
        TotalPrice DECIMAL(18,2) NOT NULL DEFAULT 0,
        Duration INT NULL,
        DurationUnit NVARCHAR(20) NULL, -- 'day', 'month', 'year'
        CreatedBy NVARCHAR(255) NULL,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ModifiedBy NVARCHAR(255) NULL,
        ModifiedDate DATETIME NULL,
        
        CONSTRAINT FK_OrderItems_Orders FOREIGN KEY (OrderOid) REFERENCES Orders(Oid) ON DELETE CASCADE
    );
    
    -- Create indexes
    CREATE INDEX IX_OrderItems_OrderOid ON OrderItems(OrderOid);
    CREATE INDEX IX_OrderItems_ItemNo ON OrderItems(OrderOid, ItemNo);
    CREATE INDEX IX_OrderItems_ProductType ON OrderItems(ProductType);
END
GO

-- =============================================
-- Sample Data for Testing
-- =============================================

-- Insert sample orders
IF NOT EXISTS (SELECT 1 FROM Orders WHERE OrderCode = 'S0949762')
BEGIN
    DECLARE @OrderOid UNIQUEIDENTIFIER = NEWID();
    
    INSERT INTO Orders (
        Oid, OrderCode, ContractCode, CustomerName, CustomerEmail, CustomerPhone,
        SubTotal, VatRate, VatAmount, TotalAmount, Status, OrderDate,
        Description, Notes, CreatedBy, CreatedDate
    )
    VALUES (
        @OrderOid, 'S0949762', 'S0949762', N'Nguyễn Văn A', '<EMAIL>', '0123456789',
        450000, 10, 45000, 495000, 1, '2022-11-18',
        N'Đơn hàng gói Usee Apps Basic', N'Khách hàng yêu cầu thanh toán qua chuyển khoản',
        'system', '2022-11-18'
    );
    
    -- Insert order items
    INSERT INTO OrderItems (
        OrderOid, ItemNo, ProductType, ProductName, ProductDescription,
        Quantity, Unit, UnitPrice, TotalPrice, Duration, DurationUnit,
        CreatedBy, CreatedDate
    )
    VALUES (
        @OrderOid, 1, N'Usee Apps', N'Usee Apps - gói Basic', N'Gói dịch vụ Usee Apps cơ bản',
        1, N'năm', 450000, 450000, 1, 'year',
        'system', '2022-11-18'
    );
END
GO

-- Insert another sample order
IF NOT EXISTS (SELECT 1 FROM Orders WHERE OrderCode = 'S0949763')
BEGIN
    DECLARE @OrderOid2 UNIQUEIDENTIFIER = NEWID();
    
    INSERT INTO Orders (
        Oid, OrderCode, ContractCode, CustomerName, CustomerEmail, CustomerPhone,
        SubTotal, VatRate, VatAmount, TotalAmount, Status, OrderDate,
        Description, Notes, CreatedBy, CreatedDate
    )
    VALUES (
        @OrderOid2, 'S0949763', 'S0949763', N'Trần Thị B', '<EMAIL>', '0987654321',
        900000, 10, 90000, 990000, 2, '2022-11-19',
        N'Đơn hàng gói Usee Apps Premium', N'Đã thanh toán',
        'system', '2022-11-19'
    );
    
    -- Insert order items
    INSERT INTO OrderItems (
        OrderOid, ItemNo, ProductType, ProductName, ProductDescription,
        Quantity, Unit, UnitPrice, TotalPrice, Duration, DurationUnit,
        CreatedBy, CreatedDate
    )
    VALUES (
        @OrderOid2, 1, N'Usee Apps', N'Usee Apps - gói Premium', N'Gói dịch vụ Usee Apps cao cấp',
        1, N'năm', 900000, 900000, 1, 'year',
        'system', '2022-11-19'
    );
END
GO

-- =============================================
-- Views for easier data access
-- =============================================

-- Create view for order summary
CREATE OR ALTER VIEW vw_OrderSummary
AS
SELECT 
    o.Oid,
    o.OrderCode,
    o.ContractCode,
    o.CustomerName,
    o.CustomerEmail,
    o.CustomerPhone,
    o.SubTotal,
    o.VatRate,
    o.VatAmount,
    o.TotalAmount,
    o.Status,
    CASE o.Status
        WHEN 0 THEN N'Nháp'
        WHEN 1 THEN N'Chờ thanh toán'
        WHEN 2 THEN N'Đã thanh toán'
        WHEN 3 THEN N'Đang xử lý'
        WHEN 4 THEN N'Hoàn thành'
        WHEN 5 THEN N'Đã hủy'
        WHEN 6 THEN N'Đã hoàn tiền'
        ELSE N'Không xác định'
    END as StatusText,
    o.OrderDate,
    o.Description,
    o.Notes,
    o.CreatedBy,
    o.CreatedDate,
    o.ModifiedBy,
    o.ModifiedDate,
    (SELECT COUNT(*) FROM OrderItems oi WHERE oi.OrderOid = o.Oid) as ItemCount,
    (SELECT TOP 1 oi.ProductType FROM OrderItems oi WHERE oi.OrderOid = o.Oid ORDER BY oi.ItemNo) as FirstProductType
FROM Orders o;
GO

-- Create view for order details with items
CREATE OR ALTER VIEW vw_OrderDetails
AS
SELECT 
    o.Oid as OrderOid,
    o.OrderCode,
    o.ContractCode,
    o.CustomerName,
    o.CustomerEmail,
    o.CustomerPhone,
    o.SubTotal,
    o.VatRate,
    o.VatAmount,
    o.TotalAmount,
    o.Status,
    CASE o.Status
        WHEN 0 THEN N'Nháp'
        WHEN 1 THEN N'Chờ thanh toán'
        WHEN 2 THEN N'Đã thanh toán'
        WHEN 3 THEN N'Đang xử lý'
        WHEN 4 THEN N'Hoàn thành'
        WHEN 5 THEN N'Đã hủy'
        WHEN 6 THEN N'Đã hoàn tiền'
        ELSE N'Không xác định'
    END as StatusText,
    o.OrderDate,
    o.Description as OrderDescription,
    o.Notes as OrderNotes,
    oi.Oid as ItemOid,
    oi.ItemNo,
    oi.ProductType,
    oi.ProductName,
    oi.ProductDescription,
    oi.Quantity,
    oi.Unit,
    oi.UnitPrice,
    oi.TotalPrice,
    oi.Duration,
    oi.DurationUnit
FROM Orders o
LEFT JOIN OrderItems oi ON o.Oid = oi.OrderOid;
GO
