
-- =============================================
-- Author:		<EMAIL>
-- Description: Thêm menu cho web
-- =============================================
CREATE procedure [dbo].[sp_client_role_menu_set]
	@userId nvarchar(450),
	@MenuRoleId uniqueidentifier,
	@MenuId uniqueidentifier,
	@WebId uniqueidentifier,
	@WebRoleId nvarchar(50),
	@IntPos int,
	@actionIds nvarchar(max)
as
	begin try
	BEGIN TRAN Client_Role_Menu_Set_transtion
		declare @tbAc TABLE 
		(
			actionId uniqueidentifier null
		)

		if exists (select MenuId from [client_role_menu] where menuRoleId = @MenuRoleId or (menuId = @MenuId and webRoleId = @WebRoleId))
		begin
			UPDATE [dbo].[client_role_menu]
			SET	  menuId = @MenuId
				  --,webId = @WebId
				  ,webRoleId = @WebRoleId
				  --,tabId = (select top 1 tabId from ClientWebTabs where tabCd = @TabCd and webId = @WebId)
				  ,intPos = @IntPos
			where menuRoleId = @MenuRoleId
				or (menuId = @MenuId and webRoleId = @WebRoleId)
		end
	else
		begin
			set @MenuRoleId = newid()
			INSERT INTO [dbo].[client_role_menu]
					   ([menuId]
					   --,webId
					   ,webRoleId
					   ,MenuRoleId
					   --,tabId
					   ,intPos
					   )
				VALUES
					   (@MenuId
					   --,@WebId
					   ,@WebRoleId
					   ,@MenuRoleId
					   --,(select top 1 tabId from ClientWebTabs where tabCd = @TabCd and webId = @WebId)
					   ,@IntPos
					   )
			
		end


		INSERT INTO @tbAc SELECT cast(part as uniqueidentifier) FROM [dbo].fn_split_string (@actionIds,',')
			

		delete t from Client_Role_Action t 
			where t.menuRoleId = @MenuRoleId 
				and (t.actionId not in (SELECT actionId FROM @tbAc))

		INSERT INTO [dbo].[Client_Role_Action]
			   ([actionId]
			   ,[menuId]
			   ,[menuRoleId]
			   ,created_dt)
		 SELECT t.actionId
		       ,@MenuId
			   ,@MenuRoleId
			   ,getdate()
			 FROM @tbAc t
			   where not exists(select 1 from [Client_Role_Action] 
					where menuRoleId = @MenuRoleId and [actionId] = t.actionId)


		select * from client_role_menu where menuRoleId = @MenuRoleId

	COMMIT TRAN Client_Role_Menu_Set_transtion
	end try
	begin catch
	ROLLBACK TRAN Client_Role_Menu_Set_transtion

		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Role_Menu_Set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'client_role_menu', 'set', @SessionID, @AddlInfo
	end catch

GO

