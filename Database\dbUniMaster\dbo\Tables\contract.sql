CREATE TABLE [dbo].[contract] (
    [id]          UNIQUEIDENTIFIER CONSTRAINT [DF_contract_id] DEFAULT (newid()) NOT NULL,
    [ord_id]      UNIQUEIDENTIFIER NOT NULL,
    [ord_dts_id]  UNIQUEIDENTIFIER NOT NULL,
    [cont_no]     NVARCHAR (50)    NULL,
    [cont_dt]     DATETIME         NULL,
    [cont_status] INT              NULL,
    [cont_link]   NVARCHAR (2000)  NULL,
    [created]     DATETIME         NULL,
    [created_by]  UNIQUEIDENTIFIER NULL,
    [updated]     DATETIME         NULL,
    [updated_by]  UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_mas_contract] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_contract_order] FOREIGN KEY ([ord_id]) REFERENCES [dbo].[order] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = <PERSON>'<PERSON><PERSON><PERSON>, <PERSON><PERSON> du<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> hủ<PERSON>', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'contract', @level2type = N'COLUMN', @level2name = N'cont_status';


GO

