
CREATE PROCEDURE [dbo].[sp_product_package_list_get] @filter NVARCHAR(500) = NULL
AS
BEGIN TRY
    --
    SELECT CONVERT(NVARCHAR(50), id) AS value,
           '[' + code + ' - ' + name +']' AS name
    FROM dbo.product_package
    WHERE 
		  ( 
			@filter IS NULL OR name LIKE N'%' + @filter + '%' 
		  )
    ORDER BY name;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_productPackage_list_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

