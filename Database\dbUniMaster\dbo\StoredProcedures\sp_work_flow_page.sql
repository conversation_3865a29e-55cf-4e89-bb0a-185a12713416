







CREATE procedure [dbo].[sp_work_flow_page]
	@userId	nvarchar(450),
	@filter nvarchar(250),
	@gridWidth			int				= 0,
	@Offset				int				= 0,
	@PageSize			int				= 10,
	@Total				int out,
	@TotalFiltered		int out,
	@gridKey		nvarchar(100) out

as
	begin try
		
		set		@Offset					= isnull(@Offset, 0)
		set		@PageSize				= isnull(@PageSize, 10)
		set		@Total					= isnull(@Total, 0)
		set		@filter					= isnull(@filter,'')

		if		@PageSize	= 0			set @PageSize	= 10
		if		@Offset		< 0			set @Offset		=  0
		
		set		@gridKey				= 'view_COR_Workflow_Page'

		select	@Total					= count(a.[wft_id])
			FROM work_flow_tb a 
			WHERE [wft_st] = 0
		set @TotalFiltered = @Total

		if @Offset = 0
		begin
			SELECT * FROM [dbo].fn_config_list_gets (@gridKey, @gridWidth) 
			ORDER BY [ordinal]
		end
		--
		SELECT a.[wft_id]
			  ,a.[wft_st]
			  ,a.[wft_dt]
			  ,a.[wft_type]
			  ,wft_type_name = n.objName
			  ,a.[wft_name]
			  ,a.[tnx_id]
			  ,a.[tnx_ref_no]
			  ,a.[tnx_remart]
			  ,a.[tnx_reason]
			  ,u.fullName as mkr_name
			  ,a.[mkr_dt]
			  ,a.mkr_id
			  ,wft_status = l.objValue1
			  ,a.tnx_object
		  FROM work_flow_tb a 			
			left join dbo.fn_config_data_gets('work_flow_status') l on a.wft_st = l.objValue
			left join dbo.fn_config_data_gets('work_type_group') n on a.[wft_type] = n.objValue
			left join client_users u on a.mkr_id = u.userId 			
			WHERE [wft_st] = 0 and a.report_to = @userId
			ORDER BY a.[mkr_dt] desc
				  offset @Offset rows	
					fetch next @PageSize rows only
	
	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_work_flow_page ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'WorkFlow', 'GET', @SessionID, @AddlInfo
	end catch

GO

