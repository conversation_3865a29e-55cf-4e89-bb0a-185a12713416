CREATE PROCEDURE [dbo].[sp_order_account_fields]
    @userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    --
    IF @id IS NOT NULL
       AND NOT EXISTS
    (
        SELECT 1
        FROM [dbo].order_account
        WHERE id = @id
    )
        SET @id = NULL;
    --begin
    --1 thong tin chung
    SELECT @id [id];
    --2- cac group
    select * 
	from dbo.fn_get_field_group('common_group')
    --3 tung o trong group
    exec sp_get_data_fields @id,'order_account'
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_account_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order_account',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

