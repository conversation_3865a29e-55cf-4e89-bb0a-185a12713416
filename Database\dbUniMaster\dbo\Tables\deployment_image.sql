CREATE TABLE [dbo].[deployment_image] (
    [id]                   UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [product_id]           UNIQUEIDENTIFIER NULL,
    [product_component_id] UNIQUEIDENTIFIER NULL,
    [project_id]           BIGINT           NULL,
    [repository_id]        BIGINT           NULL,
    [artifact_id]          BIGINT           NULL,
    [tag_id]               BIGINT           NULL,
    [tag]                  NVARCHAR (50)    NULL,
    [image]                NVARCHAR (MAX)   NULL,
    [digest]               NVARCHAR (MAX)   NULL,
    [pushed_date]          DATETIME         NULL,
    CONSTRAINT [PK_deployment_image_id] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_deployment_image_deployment_product] FOREIGN KEY ([product_id]) REFERENCES [dbo].[products] ([id]),
    CONSTRAINT [FK_deployment_image_deployment_product_component] FOREIGN KEY ([product_component_id]) REFERENCES [dbo].[deployment_product_component] ([id]),
    CONSTRAINT [FK_deployment_image_deployment_registry_artifact] FOREIGN KEY ([artifact_id]) REFERENCES [dbo].[registry_artifact] ([id])
);


GO

