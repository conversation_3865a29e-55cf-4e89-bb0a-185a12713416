CREATE TABLE [dbo].[customer_consultancy_registration] (
    [id]             UNIQUEIDENTIFIER CONSTRAINT [DF__customer_con__id__569ECEE8] DEFAULT (newid()) NOT NULL,
    [Fullname]       NVARCHAR (250)   NULL,
    [Phone]          NVARCHAR (50)    NULL,
    [Email]          NVARCHAR (50)    NULL,
    [Position]       NVARCHAR (50)    NULL,
    [CompanyName]    NVARCHAR (250)   NULL,
    [Region]         NVARCHAR (250)   NULL,
    [CompanyAddress] NVARCHAR (250)   NULL,
    [CompanySize]    NVARCHAR (50)    NULL,
    [Product]        NVARCHAR (250)   NULL,
    [JsonData]       NVARCHAR (MAX)   NULL,
    [refId]          BIGINT           NULL,
    [documentId]     VARCHAR (50)     NULL,
    [CreatedDate]    DATETIME         CONSTRAINT [DF__customer___Creat__5792F321] DEFAULT (getdate()) NULL,
    [UpdatedDate]    DATETIME         NULL,
    CONSTRAINT [PK__customer__3213E83F95CB23F5] PRIMARY KEY CLUSTERED ([id] ASC)
);


GO

