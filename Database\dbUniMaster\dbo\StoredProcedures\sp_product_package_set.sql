CREATE PROCEDURE [dbo].[sp_product_package_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @name nvarchar(50) = NULL,
    @code nvarchar(50) = NULL,
	@description nvarchar(500) = NULL,
	@active BIT = 1
AS
begin
	DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);
BEGIN TRY

    IF (@id IS NULL) or not exists(select 1 from product_package where id = @id)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.product_package
		(	id,
		    name,
		    description,
		    active,
		    created_by,
		    created,
            code
		)
		VALUES
		(   @id,
		    @name,      -- name - nvarchar(50)
		    @description,      -- description - nvarchar(100)
		    @active,     -- active - bit
		    @userId,     -- created_by - uniqueidentifier
		    GETDATE(), -- created_dt - datetime
            @code
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.product_package
		SET name = @name,
			description = @description,
			active = @active,
			updated_by = @userId,
			updated = GETDATE(),
            code = @code
        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;
	set @valid = 0
	set @messages = error_message()

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

FINAL:
    SELECT @valid valid,
           @messages AS [messages];
end

GO

