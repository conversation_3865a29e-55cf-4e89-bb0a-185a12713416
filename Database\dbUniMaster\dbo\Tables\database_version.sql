CREATE TABLE [dbo].[database_version] (
    [id]               UNIQ<PERSON><PERSON>ENTIFIER CONSTRAINT [DF_database_version_id] DEFAULT (newid()) NOT NULL,
    [product_id]       UNIQUEIDENTIFIER NULL,
    [name]             NVARCHAR (50)    NOT NULL,
    [ref_url]          NVARCHAR (4000)  NOT NULL,
    [remote_url]       NVARCHAR (4000)  NOT NULL,
    [local_path]       NVARCHAR (250)   NOT NULL,
    [is_latest]        BIT              NOT NULL,
    [previous_version] UNIQUEIDENTIFIER NULL,
    [created]          DATETIME         CONSTRAINT [DF_database_version_created] DEFAULT (getdate()) NOT NULL,
    [note]             NVARCHAR (250)   NULL,
    CONSTRAINT [PK_database_version] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_database_version_previous_version] FOREIGN KEY ([previous_version]) REFERENCES [dbo].[database_version] ([id]),
    CONSTRAINT [FK_database_version_products] FOREIGN KEY ([product_id]) REFERENCES [dbo].[products] ([id])
);


GO

