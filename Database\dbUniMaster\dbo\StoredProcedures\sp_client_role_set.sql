

CREATE procedure [dbo].[sp_client_role_set]
     @userId nvarchar(50) = null
	,@webRoleId uniqueidentifier
	,@WebId uniqueidentifier
	,@RoleCd nvarchar(50)
	,@RoleName nvarchar(200)
	,@IsAdmin bit
	,@menuIds			nvarchar(max)
as
begin
	declare @valid bit = 1
	declare @messages nvarchar(300)
	declare @tbMn TABLE 
		(
			menuId uniqueidentifier null
		)
	begin try	
	

	if not exists(select webRoleId from client_web_role WHERE webRoleId = @webRoleId and webId = @WebId)
	begin
		set @webRoleId = newid()
		INSERT INTO [dbo].[client_web_role]
				   ([WebId]
				   --,WebKey
				   ,[RoleCd]
				   ,[RoleName]
				   ,created_dt
				   ,IsAdmin
				   ,webRoleId
                   ,created_by
				   )
			 VALUES
				   (@WebId
				   --,@WebKey
				   ,@RoleCd
				   ,@RoleName
				   ,getdate()
				   ,@IsAdmin
				   ,@webRoleId
                   ,@userId
				   )
		set @messages = N'Thêm mới <PERSON>ền thành công'
	 end
	 else
	 begin
			UPDATE [dbo].client_web_role
			   SET RoleName = @RoleName
				  ,[RoleCd] = @RoleCd
				  ,isAdmin = @IsAdmin
				  --,WebKey = @WebKey
			 WHERE webRoleId = @webRoleId
				and webId = @WebId

			set @messages = N'Cập nhật Quyền thành công'
	end

	INSERT INTO @tbMn SELECT cast(part as uniqueidentifier) FROM [dbo].fn_split_string (@menuIds,',')

	delete a from client_role_action a
		join client_role_menu t on a.menuRoleId = t.menuRoleId 
		join client_web_role r on t.webRoleId = r.webRoleId
		where t.webRoleId = @webRoleId 
			and (t.menuRoleId not in (SELECT menuId FROM @tbMn))
			and r.webId = @WebId

	delete t from client_role_menu t 
		join client_web_role r on t.webRoleId = r.webRoleId
		where t.webRoleId = @webRoleId 
			and (t.menuRoleId not in (SELECT menuId FROM @tbMn))
			and r.webId = @WebId

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Role_Set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@RoleId ' + @RoleName
		set @valid = 0
		set @messages =  error_message()
		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'ClientRole', 'Insert', @SessionID, @AddlInfo
	end catch


	select @valid as valid
	      ,@messages as [messages]
		  ,@webRoleId as id
end

GO

