
-- Create the stored procedure in the specified schema
CREATE PROCEDURE [dbo].[sp_customer_details_get]
    @customerId UNIQUEIDENTIFIER
AS
BEGIN
    BEGIN TRY
        SELECT c.id
            ,fullname = c.full_name
            ,[emailAddress] = c.email_1
            ,c.created
        FROM customer c
        WHERE c.id = @customerId

    END TRY

    BEGIN CATCH
        DECLARE @ErrorNum INT
            , @ErrorMsg VARCHAR(200)
            , @ErrorProc VARCHAR(50)
            , @SessionID INT
            , @AddlInfo VARCHAR(MAX);

        SET @ErrorNum = ERROR_NUMBER();
        SET @ErrorMsg = 'sp_customer_details_get' + ERROR_MESSAGE();
        SET @ErrorProc = ERROR_PROCEDURE();
        SET @AddlInfo = '';

        EXEC utl_errorlog_set @ErrorNum
            , @ErrorMsg
            , @ErrorProc
            , 'customer'
            , 'SET'
            , @SessionID
            , @AddlInfo;
    END CATCH;
END

GO

