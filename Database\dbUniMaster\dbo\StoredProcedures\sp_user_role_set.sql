







CREATE procedure [dbo].[sp_user_role_set]
	@manId		uniqueidentifier ='85228acd-1cc9-4b5e-b12d-e36913b460c2',
	@webRoleId	uniqueidentifier = '1D7964A3-81A0-42F0-A0DA-F48D7F8B5C06',
	@userId		uniqueidentifier = '85228ACD-1CC9-4B5E-B12D-E36913B460C2',
	@userRoleId	uniqueidentifier = null,
    @orgId uniqueidentifier = 'ec46d5a6-a8f7-4bb7-a9b9-4f71dd59db77',
    @orgName nvarchar(250) = null,
    @companyId uniqueidentifier = null,
    @companyName nvarchar(250) = null
as
begin
	declare @valid bit =  1
	declare @messages nvarchar(500) = N'Thành công'
    declare @webName nvarchar(250) = ''
    set @webName =  (select top 1 a.webName  from client_webs a inner join client_web_role b on a.webId = b.webId where b.webRoleId = @webRoleId)

	begin try	
		if not exists(select 1 from client_users where userId = @userId and work_st = 2)
		begin
			set @valid = 0
			set @messages = N'Ng<PERSON>ời dùng chưa đủ điều kiện phân quyền ' + isnull(cast(@userId as varchar(100)),'null')
			goto FINAL
		end
		if not exists(select 1 from client_role_user where userRoleId = @userRoleId)
		begin
		--set @valid = 1
			if exists(select 1 from client_role_user a inner join client_web_role b on a.webId = b.webId where a.userId = @userId and b.webRoleId  = @webRoleId)
			begin
				set @valid = 0
				set @messages = N'Người dùng đã được tạo nhóm quyền cho web' + @webName + N' , không thể thêm mới'
				goto FINAL
			end
			---
			INSERT INTO [dbo].[client_role_user]
					([webRoleId]
					,[userId]
					,[webId]
					,[creationTime]
					,[creationBy]
					,[work_st]
					,[auth_by]
					,[auth_dt]
                    ,orgId
                    ,orgName
                    ,companyId
                    ,companyName
					)
				VALUES
					(@webRoleId
					,@userId
					,(select top 1 webId from client_web_role where webRoleId = @webRoleId)
					,getdate()
					,@manId
					,0
					,null
					,null
                    ,@orgId
                    ,@orgName
                    ,@companyId
                    ,@companyName
					)

		end
		else
		begin
			--if exists(select 1 from client_role_user where userId = @userId and webRoleId = @webRoleId and userRoleId <> @userRoleId)
			--begin
			--	set @valid = 0
			--	set @messages = N'Đã tồn tại quyền, không thể sửa'
			--	goto FINAL
			--end

			UPDATE [dbo].[client_role_user]
            SET
				   webRoleId = @webRoleId
                  ,orgId = @orgId
                  ,orgName = @orgName
                  ,companyId = @companyId
                  ,companyName = @companyName
			 WHERE (work_st < 2 or work_st =3)
				and userRoleId = @userRoleId

		end


	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_user_role_set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@ '  
		set @valid = 0
		set @messages = error_message()
		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'UserLogin', 'Update', @SessionID, @AddlInfo
	end catch


	FINAL:

	    select @valid as valid
			  ,@messages as [messages]

        select userId,
               convert(nvarchar(50),orgId) as organizeIds,
               b.apiUrl as baseUrl,
               b.apiKey as apiKey
        from client_role_user a inner join client_webs b on a.webId = b.webId
        where a.webRoleId = @webRoleId and a.userId = @userId

end

GO

