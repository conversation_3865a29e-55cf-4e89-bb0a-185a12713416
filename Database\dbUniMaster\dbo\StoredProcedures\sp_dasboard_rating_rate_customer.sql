
CREATE PROCEDURE [dbo].[sp_dasboard_rating_rate_customer]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(150) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @fromDate DATETIME = '2023-05-01',
    @toDate DATETIME = '2023-05-30',
    @area_Id UNIQUEIDENTIFIER = NULL -- '67DC0318-23CF-4F67-B1A8-D6AE2059EBEB'
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @filter = ISNULL(@filter, '');
	-- Lấy service_id của userId
    DECLARE @service_id UNIQUEIDENTIFIER;
    SELECT @service_id = service_id
    FROM dbo.[user]
    WHERE userId = @userId

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT 1 AS total,
           1 AS totalFiltered,
           valid = 1,
           messages = N'';
	--1. <PERSON><PERSON><PERSON> mứ<PERSON> độ đánh giá
    SELECT answer_VN AS rate_name,
           s.id AS rate_Id
    FROM survey_answer s
        JOIN survey_question q
            ON s.question_id = q.id
    WHERE question_type = 0
	AND q.service_id = @service_id
	ORDER BY rate_Id
	-- 2. Số vote tương ứng với mức độ đánh giá
    SELECT a.id AS area_id,
			name AS area_name,
           answer_id AS rate_Id,
           COUNT(answer_id) AS num_rate into #tmp
    FROM survey_result re
        LEFT JOIN dbo.survey_question sq
            ON re.question_id = sq.id
        LEFT JOIN survey_area a
            ON a.id = re.area_id
    WHERE sq.question_type = 0
	AND (@area_Id IS NULL OR @area_Id = a.id)
	AND
              (
                  CAST(re.created AS DATE) >= CAST(@fromDate AS DATE)
                  And CAST(re.created AS DATE) <= CAST(@toDate AS DATE)
              )
	AND re.area_id IS NOT NULL
	AND sq.service_id = @service_id
    GROUP BY a.id,name,
             answer_id
	ORDER BY rate_Id

    select s.id as rate_Id, ar.name, ar.id as area_id into #tmp1
    FROM survey_answer s
        JOIN survey_question q
            ON s.question_id = q.id
        CROSS APPLY (SELECT sa.id,sa.name FROM survey_area sa
		LEFT JOIN dbo.survey_organize so ON so.id = sa.organize_id
		LEFT JOIN dbo.survey_service ss ON ss.organize_id = so.id
		WHERE ss.id = @service_id) ar
    WHERE question_type = 0
	AND q.service_id = @service_id

    select a.area_id AS area_id,
			a.name AS area_name,
           a.rate_Id AS rate_Id,
           isnull(b.num_rate,0) AS num_rate
    from #tmp1 a left join #tmp b on a.rate_Id = b.rate_Id and a.area_id = b.area_id
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_dasboard_rating_rate_customer' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' @user: ' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'dasboard_rating_rate_customer',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

