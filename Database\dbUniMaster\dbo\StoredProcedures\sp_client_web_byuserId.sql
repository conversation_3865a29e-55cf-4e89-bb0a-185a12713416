



CREATE procedure [dbo].[sp_client_web_byuserId]
	@UserId nvarchar(450)

as
	begin try
		
		SELECT [WebKey]
			  ,[WebUrl]
			  ,[WebName]
			  ,[Description]
			  ,IconUrl
		FROM client_webs a
			WHERE [status] = 1
			 and (@UserId is null or exists(select 1 
							from Client_Web_Role r 
						join Client_Role_User u on r.webRoleId = u.webRoleId 
						--join Client_Web_User c on r.webId = c.webId and u.userId = c.userId 
					where u.userId = @UserId and r.WebId = a.webId)
					)
		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Web_ByUserId ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'Webs', 'Get', @SessionID, @AddlInfo
	end catch

GO

