

CREATE procedure [dbo].[sp_dashboard_get]
	@UserId			nvarchar(50)
as
	begin try
        BEGIN
            Update t
            set t.last_dt = getdate()
            from client_users t
            where userId = @UserId
        END
        
		SELECT * FROM [dbo].fn_config_list_gets ('view_client_web_grid', 0) 
		ORDER BY [ordinal]

        SELECT   [webId]
						,[webKey]
						,[webUrl]
						,[webName]
						,(select count(m.menuId) from Client_Web_Menu m where m.webId = a.webId) as menuCount
						,(select count(r.webRoleId) from Client_Web_Role r where r.webId = a.webId) as roleCount
                        ,(select count(1) from client_role_user t where t.webId = a.webId) as userCount
                        ,accessCount as accessCount
		FROM client_webs a 


        SELECT * FROM [dbo].fn_config_list_gets ('view_user_access_grid', 0) 
		ORDER BY [ordinal]

        SELECT a.[userId]
			  ,a.[avatarUrl]
			  ,a.loginName
			  ,a.fullName
			  ,a.[phone]
			  ,a.[email]
			  ,STUFF((
					  SELECT ',' + w.webName + '[' + c.roleCd + ']'
					  FROM client_role_user b 
						join client_web_role c on b.webRoleId = c.webRoleId 
						join [client_webs] w on c.webId = w.webId 
						where b.userId = a.userId -- c.isAdmin = 1 and
					  FOR XML PATH('')), 1, 1, '') as roleName
			  ,roleCount = (select count(*) from client_role_user where userid = a.userId)
              ,webCount = ((select count(distinct t.webId) from client_role_user t where t.userId = a.userId))
              ,actionLast = format(a.last_dt,'dd/MM/yyyy HH:mm:ss')
              ,actionDt = format(getdate(),'dd/MM/yyyy HH:mm:ss')
		FROM client_users a 



        SELECT * FROM [dbo].fn_config_list_gets ('view_web_role_create_grid', 0) 
		ORDER BY [ordinal]

        SELECT roleCd,
               roleName,
               r.fullName as createdBy,
               format(a.created_dt,'dd/MM/yyyy HH:mm:ss') as createdDt
        FROM client_web_role a
        	 left join client_users r on a.created_by = r.userId 
        ORDER BY a.created_dt desc




			   

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_dashboard_get ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'ClientWebs,ClientWebRoles', 'Get', @SessionID, @AddlInfo
	end catch

GO

