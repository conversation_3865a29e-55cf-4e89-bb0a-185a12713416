


-- =============================================
-- Author:		<EMAIL>
-- Description: Thêm menu cho web
-- =============================================
CREATE procedure [dbo].[sp_client_role_del]
	@userId		nvarchar(450),
	@webRoleId	uniqueidentifier,
	@webId		uniqueidentifier
as
	begin try
		begin transaction DeleteRole_tran
	    declare @valid int = 0
		declare @messages nvarchar(500) =N'Xóa quyền thành công'
		-- xóa toàn bộ một nhóm quyền
			begin
				if exists (select 1 from client_role_user where webRoleId = @webRoleId)
					begin
						set @messages = N'Nhóm quyền đã được sử dụng ! Không được phép xóa'
					end
				else
					begin
						delete a from Client_Role_Action a
							join client_role_menu t on a.menuRoleId = t.menuRoleId
							join client_web_role r on t.webRoleId = r.webRoleId
							 where t.webRoleId = @webRoleId
								and r.webId = @webId

						Delete t from client_role_menu t
							join client_web_role r on t.webRoleId = r.webRoleId
							where t.webRoleId = @webRoleId
								and r.webId = @webId
						Delete from client_web_role 
							where webRoleId = @webRoleId
								and webId = @webId

						set @valid = 1
					end	
			end
		
		select @valid as valid, @messages as messages
 
	COMMIT TRAN DeleteRole_tran
	end try
	begin catch
	ROLLBACK TRAN DeleteRole_tran

		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Menu_Del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'sp_Client_Menu_Del', 'del', @SessionID, @AddlInfo
	end catch

GO

