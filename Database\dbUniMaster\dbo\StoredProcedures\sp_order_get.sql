CREATE PROCEDURE [dbo].[sp_order_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
	@cust_id NVARCHAR(450) = NULL,

    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(o.id)
    FROM dbo.[order] o
	WHERE dbo.ufn_removeMark(o.ord_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		AND (@cust_id IS NULL OR @cust_id = o.cust_id)
          

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_order_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data
	SELECT tempB.*,tempA.sl_hopdong
	FROM 
	((SELECT o.id,COUNT(DISTINCT(co.id)) AS sl_hopdong
	FROM dbo.[order] o
	LEFT JOIN dbo.order_details od ON od.ord_id = o.id
	LEFT JOIN dbo.contract co ON co.ord_dts_id = od.id
	GROUP BY o.id) tempA
	INNER JOIN (
	SELECT DISTINCT o.id,o.cust_id,o.ord_name,o.ord_no,cd.value1 AS ord_status,cd1.value1 as ord_status_pay,
		o.created,o.created_by,o.updated,o.updated_by
	FROM dbo.[order] o
	LEFT JOIN dbo.sys_config_data cd ON cd.key_1 = 'ord_status'
	AND o.ord_status = cd.key_2
	LEFT JOIN dbo.sys_config_data cd1 ON cd1.key_1 = 'ord_status_pay'
	AND o.ord_status_pay = cd1.key_2
	WHERE dbo.ufn_removeMark(o.ord_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		AND (@cust_id IS NULL OR @cust_id = o.cust_id)
    
	) tempB ON tempB.id = tempA.id)
	ORDER BY CASE
                 WHEN tempB.updated > tempB.created THEN
                     tempB.updated
                 ELSE
                     tempB.created
             END DESC,
             tempB.ord_name DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

