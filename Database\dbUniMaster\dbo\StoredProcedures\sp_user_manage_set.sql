






CREATE procedure [dbo].[sp_user_manage_set]
	 @manId			uniqueidentifier
	,@id			uniqueidentifier = null
	,@avatarUrl		nvarchar(300)
	,@email			nvarchar(300)
	,@fullName		nvarchar(300)
	,@isSupper		int
	,@loginName		nvarchar(200)
	,@phone			nvarchar(200)
	,@position		nvarchar(200) = null
	,@userId		nvarchar(100)
	,@userRoleType	int = 0
	,@work_st		int	= null
	,@created_by	nvarchar(100) =  null
	,@created_dt	nvarchar(20) =  null
	,@auth1_by		nvarchar(100) =  null
	,@auth1_dt		nvarchar(20) =  null
	,@auth1_st		bit =  null
	,@auth2_by		nvarchar(100) =  null
	,@auth2_dt		nvarchar(20) =  null
	,@auth2_st		bit =  null
	,@delete_by		uniqueidentifier =  null
	,@delete_dt		nvarchar(20) =  null
	,@delete_st		int =  null
	,@lock_by		uniqueidentifier =  null
	,@lock_dt		nvarchar(20) =  null
	,@lock_st		int =  null
	
as
begin
	declare @valid bit =  1
	declare @messages nvarchar(50) = N'Thành công'

	begin try	
	
		--set @valid = 1
		if not exists(select 1 from client_users where userId = @userId or userId = @id)
			begin
				INSERT INTO [dbo].[client_users]
					   ([userId]
					   ,[fullName]
					   ,[loginName]
					   ,[avatarUrl]
					   ,[phone]
					   ,[email]
					   ,[isSupper]
					   ,[position]
					   ,[userRoleType]
					   ,[created_by]
					   ,[created_dt]
					   ,[lock_st]
					   ,auth1_st
					   ,auth2_st
					   ,work_st 
					   )
				 VALUES
					   (@userId
					   ,@fullName
					   ,@loginName
					   ,@avatarUrl
					   ,@phone
					   ,@email
					   ,@isSupper
					   ,@position
					   ,@userRoleType
					   ,@manId
					   ,getdate()
					   ,0
					   ,0
					   ,0
					   ,0
					   )

			end
			ELSE
			begin
				UPDATE [dbo].[client_users]
				   SET [fullName]	= @fullName
					  ,[isSupper]	= @isSupper
					  ,[avatarUrl]	= @avatarUrl
					  ,[phone]		= @phone
					  ,[email]		= @email
					  ,[position]	= @position
					  ,[userRoleType] = @userRoleType
				 WHERE  userId		= @userId 
					or userId= @id
			end
		

		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_Set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserLogin '  
		set @valid = 0
		set @messages = error_message()
		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'UserLogin', 'Update', @SessionID, @AddlInfo
	end catch


	FINAL:

	    select @valid as valid
			  ,@messages as [messages]

end

GO

