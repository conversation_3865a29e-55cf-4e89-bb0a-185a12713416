







CREATE procedure [dbo].[sp_user_manage_page]
	@userId		uniqueidentifier,
	@filter		nvarchar(250),
	@webId		uniqueidentifier,
	@userRole			int				= 0,
	@webRole			nvarchar(50)	= '',
	@gridWidth			int				= 0,
	@Offset				int				= 0,
	@PageSize			int				= 10,
	@Total				int out,
	@TotalFiltered		int out,
	@GridKey		nvarchar(100) out

as
	begin try
		

		declare @tbUsers TABLE 
		(
			userId [nvarchar](100) not null
		)

		set		@Offset					= isnull(@Offset, 0)
		set		@PageSize				= isnull(@PageSize, 10)
		set		@Total					= isnull(@Total, 0)
		set		@filter					= isnull(@filter,'')
		set		@userRole				= isnull(@userRole,0)
		set		@webRole				= isnull(@webRole,'')
		
		set		@GridKey				= 'view_user_manage_page'

		if		@PageSize	= 0			set @PageSize	= 10
		if		@Offset		< 0			set @Offset		=  0
		
		INSERT INTO @tbUsers
		select u.userId from client_web_user u 
			where (u.webId = @webId and u.UserId = @UserId)
				

		SELECT	@Total					= count(a.[UserId])
		FROM client_users a 
		WHERE (@webId is null or exists(select 1 from client_role_user u where a.userId = u.userId and u.webId = @webId))
                and (@userRole = -1 or a.userRoleType = @userRole)
                and (@webRole  = '' or exists (select 1 from client_role_user u where a.userId = u.userId and u.webRoleId = @webRole))
				and (@filter = '' or a.loginName = @filter or a.phone = @filter)

		set @TotalFiltered = @Total

		if @Offset = 0
		begin
			select * from dbo.fn_config_list_gets (@GridKey, @gridWidth - 100) 
			order by [ordinal]
		end

		--
		SELECT a.[userId]
			  ,a.[avatarUrl]
			  ,a.loginName
			  ,a.fullName
			  ,a.[phone]
			  ,a.[email]
			  ,l.objName as userRoleName
			  ,STUFF((
					  SELECT ',' + w.webName + '[' + c.roleCd + ']'
					  FROM client_role_user b 
						join client_web_role c on b.webRoleId = c.webRoleId 
						join [client_webs] w on c.webId = w.webId 
						where b.userId = a.userId -- c.isAdmin = 1 and
					  FOR XML PATH('')), 1, 1, '') as roleName
			  ,role_count = (select count(*) from client_role_user where userid = a.userId)
			  ,created_dt	= format(a.created_dt,'dd/MM/yyyy hh:mm:ss ttt')
			  ,created_by	= r.fullName 
			  ,auth1_status = au1.objValue1
			  ,auth2_status = au2.objValue1
			  ,work_status	= st.objValue1
			  ,a.lock_st
			  ,a.isSupper
			  ,lock_name	= case when a.lock_st = 1 then N'<span class="bg-warning noti-number ml5">Locked</span>' 
													else N'<span class="bg-success noti-number ml5">Actived</span>' end
		FROM client_users a 
			left join [dbo].fn_config_data_gets ('work_process_status') st on a.work_st = st.objValue
			left join [dbo].fn_config_data_gets ('object_authen_st') au1 on a.auth1_st = au1.objValue
			left join [dbo].fn_config_data_gets ('object_authen_st') au2 on a.auth2_st = au2.objValue
			left join client_users r on a.created_by = r.userId
			left join [dbo].fn_config_data_gets ('manager_user_role') l on l.objValue2 = a.userRoleType 
			WHERE (@webId is null or exists(select 1 from client_role_user u where a.userId = u.userId and u.webId = @webId))
                and (@userRole = -1 or a.userRoleType = @userRole)
                and (@webRole ='' or exists (select 1 from client_role_user u where a.userId = u.userId and u.webRoleId = @webRole))
				and (@filter = '' or a.loginName = @filter or a.phone = @filter)
			ORDER BY a.created_dt 
				  offset @Offset rows	
					fetch next @PageSize rows only
		
		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_Page ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'profile', 'GET', @SessionID, @AddlInfo
	end catch

GO

