
-- =============================================
-- Author:		AnhTT
-- Create date: 2024-04-122
-- Description:	Add/update customer consultant register
-- =============================================
CREATE PROCEDURE [dbo].[sp_webhook_strapi_receiver] @userId UNIQUEIDENTIFIER = NULL
    , @event NVARCHAR(50) = NULL
    , @model NVARCHAR(50)
    , @createdAt DATETIME
    , @entry NVARCHAR(MAX)
AS
BEGIN
    -- DECLARE @valid BIT = 1
    -- DECLARE @message NVARCHAR(100) = N''
    DECLARE @id UNIQUEIDENTIFIER
    DECLARE @refId BIGINT
        , @documentId VARCHAR(50)

    BEGIN TRY
        IF @model = 'bizzone-vn-consultation'
        BEGIN
            --customer consultant register
            DECLARE @full_name NVARCHAR(250)
                , @phone NVARCHAR(50)
                , @email NVARCHAR(250)
                , @position NVARCHAR(250)
                , @region NVARCHAR(250)
                , @companyName NVARCHAR(250)
                , @companySize NVARCHAR(250)
                , @features NVARCHAR(250)
                , @updatedDate DATETIME

            SELECT @refId = id
                , @documentId = documentId
                , @full_name = fullName
                , @phone = phoneNumber
                , @email = email
                , @position = jobPosition
                , @region = companyArea
                , @companyName = companyName
                , @companySize = sizeStaff
                , @features = features
                , @updatedDate = updatedAt
            FROM OPENJSON(@entry) WITH (
                    id BIGINT
                    , documentId VARCHAR(50)
                    , FullName NVARCHAR(250)
                    , PhoneNumber NVARCHAR(50)
                    , Email NVARCHAR(100)
                    , JobPosition NVARCHAR(250)
                    , CompanyName NVARCHAR(250)
                    , CompanyArea NVARCHAR(250)
                    , SizeStaff NVARCHAR(250)
                    , Features NVARCHAR(250)
                    , createdAt DATETIME
                    , updatedAt DATETIME
                    );

            SELECT @id = Id
            FROM customer_consultancy_registration
            WHERE documentId = @documentId

            IF @id IS NULL
            BEGIN
                INSERT INTO customer_consultancy_registration (
                    [Fullname]
                    , [Phone]
                    , [Email]
                    , [Position]
                    , [CompanyName]
                    , [Region]
                    , [CompanySize]
                    , [Product]
                    , [JsonData]
                    , CreatedDate
                    , UpdatedDate
                    , refId
                    , documentId
                    )
                VALUES (
                    @full_name
                    , @phone
                    , @email
                    , @position
                    , @companyName
                    , @region
                    , @companySize
                    , @features
                    , @entry
                    , @createdAt
                    , @updatedDate
                    , @refId
                    , @documentId
                    )

                RETURN;
            END

            UPDATE customer_consultancy_registration
            SET Fullname = @full_name
                , Phone = @phone
                , Email = @email
                , [Position] = @position
                , CompanyName = @companyName
                , Region = @region
                , CompanySize = @companySize
                , Product = @features
                , JsonData = @entry
                , UpdatedDate = @updatedDate
            WHERE id = @id

            RETURN;
        END
    END TRY

    BEGIN CATCH
        DECLARE @ErrorNum INT
            , @ErrorMsg VARCHAR(200)
            , @ErrorProc VARCHAR(50)
            , @SessionID INT
            , @AddlInfo VARCHAR(max)

        SET @ErrorNum = error_number()
        SET @ErrorMsg = 'sp_webhook_strapi_receiver ' + error_message()
        SET @ErrorProc = error_procedure()
        SET @AddlInfo = LOWER(@userId)

        -- SET @valid = 0
        -- SET @message = error_message()
        EXEC utl_ErrorLog_Set @ErrorNum
            , @ErrorMsg
            , @ErrorProc
            , 'customer_consultancy_registration'
            , 'Set'
            , @SessionID
            , @AddlInfo
    END CATCH
END

GO

