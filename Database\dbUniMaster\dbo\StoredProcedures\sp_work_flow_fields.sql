




CREATE procedure [dbo].[sp_work_flow_fields]
	@UserId		nvarchar(450),
	@wft_id		uniqueidentifier

as
	begin try
	
	select tnx_id
		  ,wft_type
		  ,wft_id
	  from work_flow_tb 
	  where wft_id = @wft_id
	
	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_work_flow_fields ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'WorkFlow', 'GET', @SessionID, @AddlInfo
	end catch

GO

