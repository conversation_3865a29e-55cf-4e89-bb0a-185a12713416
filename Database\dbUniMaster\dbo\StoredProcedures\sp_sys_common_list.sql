
CREATE PROCEDURE [dbo].[sp_sys_common_list]
	@UserID	nvarchar(450) = NULL,
	@TableName nvarchar(50) = 'district',
	@columnName NVARCHAR(50) = NULL,
    @columnParent NVARCHAR(50) = null,--'province_id',
    @valueParent NVARCHAR(50) = null,--'996AD2A3-E442-4D04-A4A8-98D6BE616765',
	@All	nvarchar(100) = NULL
AS
	BEGIN TRY		
		Declare @tSQL nvarchar(500),@tParamDefinition AS NVarchar(200) 
		SET @columnName = ISNULL(@columnName, 'name')
		
		SET @tSQL = 'SELECT LOWER(CONVERT(NVARCHAR(100),[id])) as Value
					  ,' + @columnName + ' AS Name			  
						FROM ' + @TableName +'' 
						+ ' WHERE ('
						+ @columnName + ' IS NOT NULL)'

        IF @valueParent IS NOT NULL
            BEGIN
                SET @tSQL  = @tSQL + ' AND ' + @columnParent + '= '''+ @valueParent + ''''
            END

		RAISERROR (@tSQL, 0, 1) WITH NOWAIT
		SET @tParamDefinition = '@TableName nvarchar(50)'
		EXEC sp_Executesql @tSQL, @tParamDefinition, @TableName		

	END TRY
	BEGIN CATCH
		DECLARE	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		SET @ErrorNum					= error_number()
		SET @ErrorMsg					= 'sp_sys_common_list ' + error_message()
		SET @ErrorProc					= error_procedure()

		SET @AddlInfo					= ''

		EXEC utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, @TableName, 'Get', @SessionID, @AddlInfo
	END CATCH

GO

