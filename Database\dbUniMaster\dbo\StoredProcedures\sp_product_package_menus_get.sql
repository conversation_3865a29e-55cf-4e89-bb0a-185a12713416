CREATE PROCEDURE [dbo].[sp_product_package_menus_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(300) = NULL,
    @product_package_id NVARCHAR(450) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(pm.id)
    FROM dbo.product_package_menus pm
        LEFT JOIN dbo.product_package pp
            ON pp.id = pm.package_id
    WHERE (
              @filter IS NULL
              OR dbo.ufn_removeMark(pm.description) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%'
          )
          AND
          (
              @product_package_id IS NULL
              OR pm.package_id = @product_package_id
          );

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets('view_product_package_menus_page', 0)
        ORDER BY [ordinal];
    END;
    -- Data

    SELECT pm.id,
           pm.package_id,
           pm.menu_id,
           pm.description,
           CASE
               WHEN pm.active = 1 THEN
                   N'Hoạt động'
               ELSE
                   N'Ngừng hoạt động'
           END AS activeText,
           pp.name AS package_name
    FROM dbo.product_package_menus pm
        LEFT JOIN dbo.product_package pp
            ON pp.id = pm.package_id
    WHERE (
              @filter IS NULL
              OR dbo.ufn_removeMark(pm.description) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%'
          )
          AND
          (
              @product_package_id IS NULL
              OR pm.package_id = @product_package_id
          )
    ORDER BY CASE
                 WHEN pm.updated > pm.created THEN
                     pm.updated
                 ELSE
                     pm.created
             END DESC,
             pm.description DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_menus_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package_menus',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

