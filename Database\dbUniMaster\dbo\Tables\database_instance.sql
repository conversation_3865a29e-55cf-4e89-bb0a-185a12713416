CREATE TABLE [dbo].[database_instance] (
    [id]                  UNIQ<PERSON><PERSON>ENTIFIER CONSTRAINT [DF_database_instance_id] DEFAULT (newid()) NOT NULL,
    [code]                NVARCHAR (50)    NOT NULL,
    [service_name]        NVA<PERSON>HA<PERSON> (250)   NOT NULL,
    [server]              NVA<PERSON><PERSON><PERSON> (250)   NOT NULL,
    [status]              INT              NOT NULL,
    [active]              BIT              NOT NULL,
    [number_of_databases] INT              CONSTRAINT [DEFAULT_database_instance_number_of_databases] DEFAULT ((0)) NULL,
    [created]             DATETIME         CONSTRAINT [DF_database_instance_created] DEFAULT (getdate()) NOT NULL,
    [created_by]          <PERSON><PERSON><PERSON><PERSON><PERSON>ENTIFIER NOT NULL,
    [updated]             <PERSON>ATETIM<PERSON>         NULL,
    [updated_by]          UN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>FIER NULL,
    CONSTRAINT [PK_database_instance] PRIMARY KEY CLUSTERED ([id] ASC)
);


GO

