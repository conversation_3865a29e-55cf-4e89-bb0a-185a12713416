CREATE TABLE [dbo].[deployment_resource_template] (
    [id]                   UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [product_id]           UNIQUEIDENTIFIER NULL,
    [product_component_id] UNIQUEIDENTIFIER NULL,
    [name]                 NVARCHAR (250)   NULL,
    [kind]                 NVARCHAR (100)   NULL,
    [content]              NVARCHAR (MAX)   NULL,
    [ordinal]              INT              NULL,
    [status]               INT              NULL,
    [created]              DATETIME         DEFAULT (getdate()) NULL,
    [updated]              DATETIME         NULL,
    PRIMARY KEY CLUSTERED ([id] ASC),
    FOREIGN KEY ([product_id]) REFERENCES [dbo].[products] ([id]),
    CONSTRAINT [FK_deployment_resource_template_deployment_product_component] FOREIGN KEY ([product_component_id]) REFERENCES [dbo].[deployment_product_component] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Thứ tự apply', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'deployment_resource_template', @level2type = N'COLUMN', @level2name = N'ordinal';


GO

