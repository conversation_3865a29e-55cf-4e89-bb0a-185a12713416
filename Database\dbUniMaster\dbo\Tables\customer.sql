CREATE TABLE [dbo].[customer] (
    [id]             UNIQUEIDENTIFIER CONSTRAINT [DF_customers_id] DEFAULT (newid()) NOT NULL,
    [code]           NVARCHAR (50)    NULL,
    [cif_no]         NVARCHAR (30)    NULL,
    [cust_type]      INT              NULL,
    [full_name]      NVARCHAR (100)   NOT NULL,
    [birth_day]      DATETIME         NULL,
    [avatar]         NVARCHAR (MAX)   NULL,
    [sex]            INT              NULL,
    [per_address]    NVARCHAR (100)   NULL,
    [org_name]       NVARCHAR (100)   NULL,
    [establish_date] DATETIME         NULL,
    [tax]            NVARCHAR (50)    NULL,
    [fax]            NVARCHAR (50)    NULL,
    [postion]        NVARCHAR (100)   NULL,
    [phone_1]        NVARCHAR (20)    NULL,
    [phone_2]        NVARCHAR (20)    NULL,
    [email_1]        NVARCHAR (100)   NULL,
    [email_2]        NVARCHAR (100)   NULL,
    [org_address]    NVARCHAR (250)   NULL,
    [provinve_id]    UNIQUEIDENTIFIER NULL,
    [district_id]    UNIQUEIDENTIFIER NULL,
    [national_id]    UNIQUEIDENTIFIER NULL,
    [description]    NVARCHAR (250)   NULL,
    [consultancy_id] UNIQUEIDENTIFIER NULL,
    [created]        DATETIME         CONSTRAINT [DF_customer_created] DEFAULT (getdate()) NULL,
    [created_by]     UNIQUEIDENTIFIER NULL,
    [updated]        DATETIME         NULL,
    [updated_by]     UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_customers] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_customer_customer_consultancy_registration] FOREIGN KEY ([consultancy_id]) REFERENCES [dbo].[customer_consultancy_registration] ([id]),
    CONSTRAINT [FK_customer_district] FOREIGN KEY ([district_id]) REFERENCES [dbo].[district] ([id]),
    CONSTRAINT [FK_customer_national] FOREIGN KEY ([national_id]) REFERENCES [dbo].[national] ([id]),
    CONSTRAINT [FK_customer_province] FOREIGN KEY ([provinve_id]) REFERENCES [dbo].[province] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'0: cá nhân, 1. doanh nghiệp', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'customer', @level2type = N'COLUMN', @level2name = N'cust_type';


GO

