CREATE PROCEDURE [dbo].[sp_product_package_fields]
    @userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = '57768d11-d62b-47e4-a37e-839cd10f91c6'
AS
BEGIN TRY
    --
    SELECT *
	INTO #product_package
	FROM product_package
	WHERE id = @id

	IF @ID is not null and not exists(SELECT 1 FROM #product_package ) SET @id = null

	select @id id
		  ,'product_package' as tableKey
		  ,groupKey = 'common_group'
	--2- cac group
	select * 
	from dbo.fn_get_field_group('common_group')
	
	if @id IS NOT NULL
	begin

		SELECT a.[id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			 ,CASE [data_type] 
				  WHEN 'nvarchar' THEN CONVERT(NVARCHAR(350), CASE [field_name] 
                             WHEN 'name' THEN b.name
                             WHEN 'description' THEN b.description
							 when 'code' then b.code
						END) 
				  WHEN 'bit' THEN CASE [field_name] 
							WHEN 'active' THEN CASE WHEN active = 1 THEN 'true' ELSE 'false' END END
                 
				 WHEN 'uniqueidentifier' THEN convert(NVARCHAR(350), CASE [field_name] 
							WHEN 'id' THEN (cast(b.id AS VARCHAR(50))) 
					  END)
       --         ELSE convert(NVARCHAR(50), CASE [field_name] 
					  --WHEN 'active' THEN b.active
					  --END)
				END AS columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject] 
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM #product_package b
			OUTER APPLY
				(
					SELECT *
					FROM sys_config_form
					WHERE table_name = 'product_package' 
				)a
		  where 
			(isvisiable = 1 or isRequire = 1)
			
		  order by ordinal
	
	end
	else
	begin
		
		SELECT [id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,group_cd
			  ,columnDefault as columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject]
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM sys_config_form a
		  where table_name = 'product_package' 
		  and (isvisiable = 1 or isRequire = 1)
		  order by ordinal

		--2
	end
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_field' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

