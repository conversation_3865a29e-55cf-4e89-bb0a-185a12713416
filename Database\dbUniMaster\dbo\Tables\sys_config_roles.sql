CREATE TABLE [dbo].[sys_config_roles] (
    [id]          UNIQUEIDENTIFIER CONSTRAINT [DF_sys_config_menu_roles_WebRoleId] DEFAULT (newid()) NOT NULL,
    [roleName]    NVARCHAR (200)   NOT NULL,
    [roleType]    INT              NULL,
    [description] NVARCHAR (250)   NULL,
    [isAdmin]     BIT              NULL,
    [created]     DATETIME         CONSTRAINT [DF_sys_config_menu_roles_CreationTime] DEFAULT (getdate()) NOT NULL,
    [created_by]  NVARCHAR (100)   NULL,
    [updated]     DATETIME         NULL,
    [updated_by]  NVARCHAR (100)   NULL,
    CONSTRAINT [PK_sys_config_menu_roles] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_sys_config_roles_sys_config_role_type] FOREIGN KEY ([roleType]) REFERENCES [dbo].[sys_config_role_type] ([id])
);


GO

