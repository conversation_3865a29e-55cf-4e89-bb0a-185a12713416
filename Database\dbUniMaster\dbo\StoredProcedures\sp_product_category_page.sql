
CREATE PROCEDURE [dbo].[sp_product_category_page]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
	@id uniqueidentifier,
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(id)
    FROM dbo.product_category
    WHERE prod_line_id = @id
		and (@filter = '' or [category_name] LIKE N'%' + dbo.ufn_removeMark(@filter) + '%');
          

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_product_category_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data

    SELECT [id]
		  ,[category_name]
		  ,[category_desc]
		  ,[prod_line_id]
		  ,[int_ord]
		  ,[created]
		  ,[created_by]
		  ,[updated]
		  ,[updated_by]
		  ,[app_st]
          ,count_product = isnull((select count(1) from products t where t.prod_line_id = pl.id),0)
	FROM dbo.product_category pl
	WHERE prod_line_id = @id
		and (@filter = '' or [category_name] LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
    ORDER BY pl.updated,
             pl.[category_name] DESC 
		OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_line_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_line',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

