CREATE PROCEDURE [dbo].[sp_order_detail_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
	@order_id NVARCHAR(450) = NULL,

    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(o.id)
    FROM dbo.[order_details] o
	--WHERE dbo.ufn_removeMark(o.) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
	--	AND (@cust_id IS NULL OR @cust_id = o.cust_id)
          

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_order_details_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data
	SELECT od.*, p.prod_name,pack.name AS package_name
	FROM dbo.order_details od
	LEFT JOIN dbo.products p ON od.prod_id = p.id
	LEFT JOIN dbo.product_package pack ON od.package_id = pack.id
	ORDER BY CASE
                 WHEN od.updated > od.created THEN
                     od.updated
                 ELSE
                     od.created
             END DESC,
             od.start_dt DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_detail_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'detail',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

