CREATE PROCEDURE [dbo].[sp_product_package_details_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(300) = NULL,
	@product_id NVARCHAR(450) = NULL,
	@product_package_id NVARCHAR(450) = NULL,
	@productLine_id NVARCHAR(450) = NULL,

    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    --SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;
	
    SELECT @Total = COUNT(pd.id)
    FROM dbo.product_package_details pd
	LEFT JOIN dbo.product_package pp ON pd.id = pd.package_id
	LEFT JOIN  dbo.products p ON p.id = pd.prod_id
	LEFT JOIN dbo.product_line pl ON pl.id = p.prod_line_id
    WHERE (@filter IS NULL OR dbo.ufn_removeMark(pd.description) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
	AND (@product_id IS NULL OR p.id = @product_id)
	AND (@product_package_id IS NULL OR pp.id = @product_package_id)
	AND (@productLine_id IS NULL OR pl.id = @productLine_id)    

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_product_package_details_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data

    SELECT pd.*,p.prod_name AS prod_name, pp.name AS package_name
	FROM dbo.product_package_details pd
	LEFT JOIN dbo.product_package pp ON pd.id = pd.package_id
	LEFT JOIN  dbo.products p ON p.id = pd.prod_id
	LEFT JOIN dbo.product_line pl ON pl.id = p.prod_line_id
    WHERE (@filter IS NULL OR dbo.ufn_removeMark(pd.description) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
	AND (@product_id IS NULL OR @product_id = pd.prod_id)
	AND (@product_package_id IS NULL OR @product_package_id = pd.package_id )
	AND (@productLine_id IS NULL OR @productLine_id = pl.id)  
    ORDER BY CASE
                 WHEN pd.updated > pd.created THEN
                     pd.updated
                 ELSE
                     pd.created
             END DESC,
             pd.description DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_details_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package_details',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

