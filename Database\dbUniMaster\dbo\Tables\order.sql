CREATE TABLE [dbo].[order] (
    [id]             UNIQUEIDENTIFIER NOT NULL,
    [cust_id]        UNIQUEIDENTIFIER NULL,
    [ord_name]       NVARCHAR (50)    NULL,
    [ord_no]         NVARCHAR (50)    NULL,
    [ord_status]     BIT              NULL,
    [ord_status_pay] BIT              NULL,
    [voucher_no]     NVARCHAR (50)    NULL,
    [jump]           INT              CONSTRAINT [DF_order_jump] DEFAULT ((1)) NULL,
    [created]        DATETIME         NULL,
    [created_by]     UNIQUEIDENTIFIER NULL,
    [updated]        DATETIME         NULL,
    [updated_by]     UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_customer_prod_register] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_order_customer] FOREIGN KEY ([cust_id]) REFERENCES [dbo].[customer] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Chờ thành toán, <PERSON><PERSON><PERSON> thành, <PERSON><PERSON> hủy', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order', @level2type = N'COLUMN', @level2name = N'ord_status';


GO

