

CREATE procedure [dbo].[sp_config_gridview_del]
	 @UserId	nvarchar(450)
	,@id		bigint
as
	begin try	
		begin
			if not exists (select id from sys_config_list where id = @id)
				begin
					delete d from sys_config_list d  where id = @id
				end
	    end
		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_CA_PB_Grid_Del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserId	' 

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'sys_config_list', 'Del', @SessionID, @AddlInfo
	end catch

GO

