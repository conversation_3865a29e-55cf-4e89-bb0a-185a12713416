
CREATE PROCEDURE [dbo].[sp_products_list_get] 
@product_line_id NVARCHAR(500) = NULL,
@filter NVARCHAR(500) = NULL
AS
BEGIN TRY
    --
    SELECT CONVERT(NVARCHAR(500), id) AS value,
           prod_name AS name
    FROM dbo.products
    WHERE (
              @product_line_id IS NULL
              OR prod_line_id = @product_line_id
          )
		  AND 
		  ( 
			@filter IS NULL OR dbo.ufn_removeMark(prod_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		  )
    ORDER BY prod_name;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_products_list_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'products',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

