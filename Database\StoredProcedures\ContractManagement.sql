-- =============================================
-- Contract Management Stored Procedure for Personal Portal
-- =============================================

-- =============================================
-- Stored Procedure: sp_personal_contract_page
-- Description: Get paginated list of contracts
-- =============================================
CREATE OR ALTER PROCEDURE sp_personal_contract_page
    @userId UNIQUEIDENTIFIER = NULL,
    @ContractNo NVARCHAR(50) = NULL,
    @OrderCode NVARCHAR(50) = NULL,
    @CustomerName NVARCHAR(255) = NULL,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @Status INT = NULL,
    @filter NVARCHAR(30) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @gridWidth INT = 0,
    @gridKey NVARCHAR(100) = NULL OUT,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY
    SET @gridKey = 'view_personal_contract_page'
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    IF @PageSize <= 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    -- Get total count
    SELECT @Total = COUNT(c.id)
    FROM dbo.[contract] c
    LEFT JOIN dbo.[order_details] od ON od.id = c.ord_dts_id
    LEFT JOIN dbo.[order] o ON o.id = od.ord_id
    LEFT JOIN dbo.customer cust ON cust.id = o.cust_id
    WHERE (@ContractNo IS NULL OR c.cont_no LIKE '%' + @ContractNo + '%')
        AND (@OrderCode IS NULL OR o.ord_no LIKE '%' + @OrderCode + '%')
        AND (@CustomerName IS NULL OR cust.cust_name LIKE '%' + @CustomerName + '%')
        AND (@FromDate IS NULL OR c.cont_dt >= @FromDate)
        AND (@ToDate IS NULL OR c.cont_dt <= @ToDate)
        AND (@Status IS NULL OR c.cont_status = @Status)
        AND (dbo.ufn_removeMark(c.cont_no) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%');

    SET @TotalFiltered = @Total;

    -- Return grid configuration if first page
    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets(@gridKey, 0)
        ORDER BY [ordinal];
    END;

    -- Return data
    SELECT 
        c.id as Oid,
        o.cust_id as CustomerId,
        c.ord_id as OrderId,
        c.ord_dts_id as OrderDetailsId,
        c.cont_no as ContractNo,
        FORMAT(c.cont_dt, 'dd/MM/yyyy') as ContractDate,
        cd.value1 AS StatusText,
        cd1.value1 AS OrderStatusText,
        cd2.value1 AS OrderPaymentStatusText,
        o.ord_no + '_' + o.ord_name AS OrderName,
        od.start_dt as StartDate,
        od.end_dt as EndDate,
        o.ord_status as OrderStatus,
        o.ord_status_pay as OrderPaymentStatus
    FROM dbo.[contract] c
    LEFT JOIN dbo.[order_details] od ON od.id = c.ord_dts_id
    LEFT JOIN dbo.[order] o ON o.id = od.ord_id
    LEFT JOIN dbo.customer cust ON cust.id = o.cust_id
    LEFT JOIN dbo.sys_config_data cd ON cd.key_1 = 'cont_status' AND cd.key_2 = c.cont_status
    LEFT JOIN dbo.sys_config_data cd1 ON cd1.key_1 = 'ord_status' AND cd1.key_2 = o.ord_status
    LEFT JOIN dbo.sys_config_data cd2 ON cd2.key_1 = 'ord_status_pay' AND cd2.key_2 = o.ord_status_pay
    WHERE (@ContractNo IS NULL OR c.cont_no LIKE '%' + @ContractNo + '%')
        AND (@OrderCode IS NULL OR o.ord_no LIKE '%' + @OrderCode + '%')
        AND (@CustomerName IS NULL OR cust.cust_name LIKE '%' + @CustomerName + '%')
        AND (@FromDate IS NULL OR c.cont_dt >= @FromDate)
        AND (@ToDate IS NULL OR c.cont_dt <= @ToDate)
        AND (@Status IS NULL OR c.cont_status = @Status)
        AND (dbo.ufn_removeMark(c.cont_no) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
    ORDER BY CASE
                 WHEN c.updated > c.created THEN c.updated
                 ELSE c.created
             END DESC,
             c.cont_no DESC 
    OFFSET @Offset ROWS 
    FETCH NEXT @PageSize ROWS ONLY

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'contract',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH
GO
