

CREATE PROCEDURE [dbo].[sp_product_category_list] @filter NVARCHAR(500) = NULL
AS
BEGIN TRY
    --
    SELECT LOWER(CONVERT(NVARCHAR(500), id)) AS value,
           category_name AS name
    FROM dbo.product_category
    WHERE (
              @filter IS NULL
              OR (dbo.ufn_removeMark(category_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%')
          )
    ORDER BY category_name;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_category_list_get' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_category',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

