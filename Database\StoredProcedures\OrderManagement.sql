-- =============================================
-- Order Management Stored Procedures
-- =============================================

-- =============================================
-- Stored Procedure: sp_personal_order_page
-- Description: Get paginated list of orders
-- =============================================
CREATE OR ALTER PROCEDURE sp_personal_order_page
    @PageIndex INT = 1,
    @PageSize INT = 10,
    @OrderCode NVARCHAR(50) = NULL,
    @ContractCode NVARCHAR(50) = NULL,
    @CustomerName NVARCHAR(255) = NULL,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @Status INT = NULL,
    @ProductType NVARCHAR(100) = NULL,
    @SortField NVARCHAR(50) = 'CreatedDate',
    @SortDirection NVARCHAR(4) = 'DESC'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageIndex - 1) * @PageSize;
    
    -- Get total count
    DECLARE @TotalCount INT;
    
    SELECT @TotalCount = COUNT(*)
    FROM Orders o
    WHERE (@OrderCode IS NULL OR o.OrderCode LIKE '%' + @OrderCode + '%')
        AND (@ContractCode IS NULL OR o.ContractCode LIKE '%' + @ContractCode + '%')
        AND (@CustomerName IS NULL OR o.CustomerName LIKE '%' + @CustomerName + '%')
        AND (@FromDate IS NULL OR o.OrderDate >= @FromDate)
        AND (@ToDate IS NULL OR o.OrderDate <= @ToDate)
        AND (@Status IS NULL OR o.Status = @Status)
        AND (@ProductType IS NULL OR EXISTS (
            SELECT 1 FROM OrderItems oi 
            WHERE oi.OrderOid = o.Oid AND oi.ProductType LIKE '%' + @ProductType + '%'
        ));
    
    -- Get paginated data
    SELECT 
        o.Oid,
        o.OrderCode,
        o.ContractCode,
        o.CustomerName,
        o.CustomerEmail,
        ISNULL((SELECT TOP 1 oi.ProductType FROM OrderItems oi WHERE oi.OrderOid = o.Oid), '') as ProductType,
        o.TotalAmount,
        o.Status,
        CASE o.Status
            WHEN 0 THEN N'Nháp'
            WHEN 1 THEN N'Chờ thanh toán'
            WHEN 2 THEN N'Đã thanh toán'
            WHEN 3 THEN N'Đang xử lý'
            WHEN 4 THEN N'Hoàn thành'
            WHEN 5 THEN N'Đã hủy'
            WHEN 6 THEN N'Đã hoàn tiền'
            ELSE N'Không xác định'
        END as StatusText,
        o.OrderDate,
        o.CreatedDate,
        o.CreatedBy,
        @TotalCount as TotalCount
    FROM Orders o
    WHERE (@OrderCode IS NULL OR o.OrderCode LIKE '%' + @OrderCode + '%')
        AND (@ContractCode IS NULL OR o.ContractCode LIKE '%' + @ContractCode + '%')
        AND (@CustomerName IS NULL OR o.CustomerName LIKE '%' + @CustomerName + '%')
        AND (@FromDate IS NULL OR o.OrderDate >= @FromDate)
        AND (@ToDate IS NULL OR o.OrderDate <= @ToDate)
        AND (@Status IS NULL OR o.Status = @Status)
        AND (@ProductType IS NULL OR EXISTS (
            SELECT 1 FROM OrderItems oi 
            WHERE oi.OrderOid = o.Oid AND oi.ProductType LIKE '%' + @ProductType + '%'
        ))
    ORDER BY 
        CASE WHEN @SortField = 'OrderCode' AND @SortDirection = 'ASC' THEN o.OrderCode END ASC,
        CASE WHEN @SortField = 'OrderCode' AND @SortDirection = 'DESC' THEN o.OrderCode END DESC,
        CASE WHEN @SortField = 'CustomerName' AND @SortDirection = 'ASC' THEN o.CustomerName END ASC,
        CASE WHEN @SortField = 'CustomerName' AND @SortDirection = 'DESC' THEN o.CustomerName END DESC,
        CASE WHEN @SortField = 'TotalAmount' AND @SortDirection = 'ASC' THEN o.TotalAmount END ASC,
        CASE WHEN @SortField = 'TotalAmount' AND @SortDirection = 'DESC' THEN o.TotalAmount END DESC,
        CASE WHEN @SortField = 'OrderDate' AND @SortDirection = 'ASC' THEN o.OrderDate END ASC,
        CASE WHEN @SortField = 'OrderDate' AND @SortDirection = 'DESC' THEN o.OrderDate END DESC,
        CASE WHEN @SortField = 'CreatedDate' AND @SortDirection = 'ASC' THEN o.CreatedDate END ASC,
        CASE WHEN @SortField = 'CreatedDate' AND @SortDirection = 'DESC' THEN o.CreatedDate END DESC,
        o.CreatedDate DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- =============================================
-- Stored Procedure: sp_personal_order_GetInfo
-- Description: Get order information by ID
-- =============================================
CREATE OR ALTER PROCEDURE sp_personal_order_GetInfo
    @Oid UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        o.Oid,
        o.OrderCode,
        o.ContractCode,
        o.CustomerName,
        o.CustomerEmail,
        o.CustomerPhone,
        o.SubTotal,
        o.VatRate,
        o.VatAmount,
        o.TotalAmount,
        o.Status,
        CASE o.Status
            WHEN 0 THEN N'Nháp'
            WHEN 1 THEN N'Chờ thanh toán'
            WHEN 2 THEN N'Đã thanh toán'
            WHEN 3 THEN N'Đang xử lý'
            WHEN 4 THEN N'Hoàn thành'
            WHEN 5 THEN N'Đã hủy'
            WHEN 6 THEN N'Đã hoàn tiền'
            ELSE N'Không xác định'
        END as StatusText,
        o.OrderDate,
        o.CreatedDate,
        o.Description,
        o.Notes,
        o.CreatedBy,
        o.CreatedDate,
        o.ModifiedBy,
        o.ModifiedDate
    FROM Orders o
    WHERE o.Oid = @Oid;
END
GO

-- =============================================
-- Stored Procedure: sp_personal_order_GetDetail
-- Description: Get detailed order information including items
-- =============================================
CREATE OR ALTER PROCEDURE sp_personal_order_GetDetail
    @Oid UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Get order header
    SELECT 
        o.Oid,
        o.OrderCode,
        o.ContractCode,
        o.OrderDate,
        o.CreatedDate,
        o.CustomerName,
        o.CustomerEmail,
        o.CustomerPhone,
        o.Status,
        CASE o.Status
            WHEN 0 THEN N'Nháp'
            WHEN 1 THEN N'Chờ thanh toán'
            WHEN 2 THEN N'Đã thanh toán'
            WHEN 3 THEN N'Đang xử lý'
            WHEN 4 THEN N'Hoàn thành'
            WHEN 5 THEN N'Đã hủy'
            WHEN 6 THEN N'Đã hoàn tiền'
            ELSE N'Không xác định'
        END as StatusText,
        o.SubTotal,
        o.VatRate,
        o.VatAmount,
        o.TotalAmount,
        o.Description,
        o.Notes
    FROM Orders o
    WHERE o.Oid = @Oid;
    
    -- Get order items
    SELECT 
        oi.ItemNo,
        oi.ProductType,
        oi.ProductName,
        oi.ProductDescription,
        oi.Quantity,
        oi.Unit,
        oi.UnitPrice,
        oi.TotalPrice,
        oi.Duration,
        oi.DurationUnit
    FROM OrderItems oi
    WHERE oi.OrderOid = @Oid
    ORDER BY oi.ItemNo;
END
GO

-- =============================================
-- Stored Procedure: sp_personal_order_SetInfo
-- Description: Create or update order
-- =============================================
CREATE OR ALTER PROCEDURE sp_personal_order_SetInfo
    @Oid UNIQUEIDENTIFIER = NULL,
    @OrderCode NVARCHAR(50),
    @ContractCode NVARCHAR(50) = NULL,
    @CustomerName NVARCHAR(255),
    @CustomerEmail NVARCHAR(255) = NULL,
    @CustomerPhone NVARCHAR(50) = NULL,
    @SubTotal DECIMAL(18,2) = 0,
    @VatRate DECIMAL(5,2) = 10,
    @VatAmount DECIMAL(18,2) = 0,
    @TotalAmount DECIMAL(18,2) = 0,
    @Status INT = 0,
    @OrderDate DATETIME = NULL,
    @Description NVARCHAR(MAX) = NULL,
    @Notes NVARCHAR(MAX) = NULL,
    @CreatedBy NVARCHAR(255) = NULL,
    @ModifiedBy NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @IsUpdate BIT = 0;
    DECLARE @CurrentDate DATETIME = GETDATE();

    -- Check if this is an update
    IF @Oid IS NOT NULL AND EXISTS(SELECT 1 FROM Orders WHERE Oid = @Oid)
    BEGIN
        SET @IsUpdate = 1;
    END
    ELSE
    BEGIN
        SET @Oid = NEWID();
        SET @OrderDate = ISNULL(@OrderDate, @CurrentDate);
    END

    -- Validate required fields
    IF @OrderCode IS NULL OR @OrderCode = ''
    BEGIN
        SELECT 0 as valid, 'Order code is required' as message;
        RETURN;
    END

    IF @CustomerName IS NULL OR @CustomerName = ''
    BEGIN
        SELECT 0 as valid, 'Customer name is required' as message;
        RETURN;
    END

    -- Check for duplicate order code (excluding current record)
    IF EXISTS(SELECT 1 FROM Orders WHERE OrderCode = @OrderCode AND Oid != @Oid)
    BEGIN
        SELECT 0 as valid, 'Order code already exists' as message;
        RETURN;
    END

    BEGIN TRY
        BEGIN TRANSACTION;

        IF @IsUpdate = 1
        BEGIN
            -- Update existing order
            UPDATE Orders
            SET
                OrderCode = @OrderCode,
                ContractCode = @ContractCode,
                CustomerName = @CustomerName,
                CustomerEmail = @CustomerEmail,
                CustomerPhone = @CustomerPhone,
                SubTotal = @SubTotal,
                VatRate = @VatRate,
                VatAmount = @VatAmount,
                TotalAmount = @TotalAmount,
                Status = @Status,
                OrderDate = @OrderDate,
                Description = @Description,
                Notes = @Notes,
                ModifiedBy = @ModifiedBy,
                ModifiedDate = @CurrentDate
            WHERE Oid = @Oid;
        END
        ELSE
        BEGIN
            -- Insert new order
            INSERT INTO Orders (
                Oid, OrderCode, ContractCode, CustomerName, CustomerEmail, CustomerPhone,
                SubTotal, VatRate, VatAmount, TotalAmount, Status, OrderDate,
                Description, Notes, CreatedBy, CreatedDate
            )
            VALUES (
                @Oid, @OrderCode, @ContractCode, @CustomerName, @CustomerEmail, @CustomerPhone,
                @SubTotal, @VatRate, @VatAmount, @TotalAmount, @Status, @OrderDate,
                @Description, @Notes, @CreatedBy, @CurrentDate
            );
        END

        COMMIT TRANSACTION;

        SELECT 1 as valid, 'Order saved successfully' as message, @Oid as data;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SELECT 0 as valid, ERROR_MESSAGE() as message;
    END CATCH
END
GO

-- =============================================
-- Stored Procedure: sp_personal_order_Delete
-- Description: Delete order
-- =============================================
CREATE OR ALTER PROCEDURE sp_personal_order_Delete
    @Oid UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if order exists
    IF NOT EXISTS(SELECT 1 FROM Orders WHERE Oid = @Oid)
    BEGIN
        SELECT 0 as valid, 'Order not found' as message;
        RETURN;
    END

    -- Check if order can be deleted (business rules)
    DECLARE @Status INT;
    SELECT @Status = Status FROM Orders WHERE Oid = @Oid;

    IF @Status IN (2, 3, 4) -- Paid, Processing, Completed
    BEGIN
        SELECT 0 as valid, 'Cannot delete order with current status' as message;
        RETURN;
    END

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Delete order items first
        DELETE FROM OrderItems WHERE OrderOid = @Oid;

        -- Delete order
        DELETE FROM Orders WHERE Oid = @Oid;

        COMMIT TRANSACTION;

        SELECT 1 as valid, 'Order deleted successfully' as message;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SELECT 0 as valid, ERROR_MESSAGE() as message;
    END CATCH
END
GO

-- =============================================
-- Stored Procedure: sp_personal_order_UpdateStatus
-- Description: Update order status
-- =============================================
CREATE OR ALTER PROCEDURE sp_personal_order_UpdateStatus
    @Oid UNIQUEIDENTIFIER,
    @Status INT,
    @ModifiedBy NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if order exists
    IF NOT EXISTS(SELECT 1 FROM Orders WHERE Oid = @Oid)
    BEGIN
        SELECT 0 as valid, 'Order not found' as message;
        RETURN;
    END

    -- Validate status value
    IF @Status NOT IN (0, 1, 2, 3, 4, 5, 6)
    BEGIN
        SELECT 0 as valid, 'Invalid status value' as message;
        RETURN;
    END

    BEGIN TRY
        UPDATE Orders
        SET
            Status = @Status,
            ModifiedBy = @ModifiedBy,
            ModifiedDate = GETDATE()
        WHERE Oid = @Oid;

        SELECT 1 as valid, 'Order status updated successfully' as message;

    END TRY
    BEGIN CATCH
        SELECT 0 as valid, ERROR_MESSAGE() as message;
    END CATCH
END
GO
