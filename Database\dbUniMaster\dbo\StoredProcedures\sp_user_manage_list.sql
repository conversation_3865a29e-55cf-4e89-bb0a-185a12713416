
CREATE procedure [dbo].[sp_user_manage_list]
	@clientId	nvarchar(100),
	@userId		nvarchar(450),
	@userRole		int,
	@filter		nvarchar(100) = '',
	@byreport	int = 0
as
	begin try
		
		set @filter = isnull(@filter,'')
		--
		SELECT cast(a.[userId] as nvarchar(50)) as value
			  ,a.[avatarUrl]
			  ,a.loginName
			  ,a.[fullName] as name
			  ,a.[phone] as[phone]
			  ,a.[email] as [email]
		FROM client_users a 
		 --join client_role_user b on a.userId = b.UserId 
		 --join client_web_role c on b.webRoleId = c.webRoleId 
		 --join client_webs d on c.WebId = d.WebId 
		 --join client_web_user u on a.userId = u.UserId and c.webId = u.WebId
		 left join client_users r on a.created_by = r.userId
			WHERE (a.userRoleType = 1)
				--and (u.userId = @userId or u.reportId = @userId or exists(select userid from [dbAppManager].[dbo].[ClientUser] where userId = @userId) or @byreport = 1)
				--and (d.clientId = @clientId or d.clientIdDev = @clientId 
				--	or ((@clientId = 'swagger' or @clientId ='swagger_development') and d.clientId = 'web_s_core_prod')
				--	)
				and (@filter = '' or a.loginName like @filter or a.phone like @filter)
			ORDER BY a.loginName 
	
	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_List ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'profile', 'GET', @SessionID, @AddlInfo
	end catch

GO

