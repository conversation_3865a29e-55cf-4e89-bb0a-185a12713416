
-- =============================================
-- Author:		AnhTT
-- Create date: 2024-04-122
-- Description:	selection list
-- =============================================
CREATE PROCEDURE [dbo].[sp_common_selection_list] @UserId NVARCHAR(50) = NULL
    , @refId NVARCHAR(50) = NULL
    , @key NVARCHAR(50)
    , @search NVARCHAR(100) = NULL
    , @RecordsTotal INT = 0 OUT
    , @RecordsFiltered INT = 0 OUT
AS
BEGIN TRY
    -- repository
    IF @key = 'register_repository'
    BEGIN
        SELECT [name] = CONCAT (
                b.name
                , '/'
                , a.name
                )
            , [value] = LOWER(a.id)
            , icon = 'uni-icon'
            , icon_is = 0
            , isHtml = 1
        FROM registry_repository a
        INNER JOIN registry_project b
            ON a.project_id = b.id
        WHERE @search IS NULL
            OR b.name LIKE @search + '%'
            OR a.name LIKE @search + '%'

        SET @RecordsFiltered = @@ROWCOUNT
        SET @RecordsTotal = @RecordsFiltered

        RETURN;
    END

    --product
    IF @key = 'product'
    BEGIN
        SELECT [name] = a.prod_name + CONCAT (
                ' ('
                , a.prod_code
                , ')'
                )
            , [value] = dbo.fn_guid_to_string(a.id)
            , icon = 'uni-icon'
            , icon_is = 0
            , isHtml = 1
        FROM products a
        WHERE @search IS NULL
            OR a.prod_code LIKE @search + '%'
            OR a.prod_name LIKE @search + '%'

        SET @RecordsFiltered = @@ROWCOUNT
        SET @RecordsTotal = @RecordsFiltered

        RETURN;
    END

    --product module
    IF @key = 'product_modules'
    BEGIN
        SELECT [name] = a.name
            , [value] = dbo.fn_guid_to_string(a.id)
            , icon = 'uni-icon'
            , icon_is = 0
            , isHtml = 1
        FROM product_module a
        WHERE a.product_id = @refId
            AND (
                @search IS NULL
                OR a.code LIKE @search + '%'
                OR a.name LIKE @search + '%'
                )

        SET @RecordsFiltered = @@ROWCOUNT
        SET @RecordsTotal = @RecordsFiltered

        RETURN;
    END

    -- nations
    IF @key = 'nation'
    BEGIN
        SELECT [name] = a.name
            , [value] = dbo.fn_guid_to_string(a.id)
            , icon = 'uni-icon'
            , icon_is = 0
            , isHtml = 1
        FROM [national] a
        WHERE @search IS NULL
            OR a.name LIKE @search + '%'

        SET @RecordsFiltered = @@ROWCOUNT
        SET @RecordsTotal = @RecordsFiltered

        RETURN;
    END

    -- provinces
    IF @key = 'province'
    BEGIN
        SELECT [name] = a.name
            , [value] = dbo.fn_guid_to_string(a.id)
            , icon = 'uni-icon'
            , icon_is = 0
            , isHtml = 1
        FROM [province] a
        WHERE (
                @search IS NULL
                OR a.name LIKE @search + '%'
                )
            AND (
                @refId IS NULL
                OR national_id = @refId
                )

        SET @RecordsFiltered = @@ROWCOUNT
        SET @RecordsTotal = @RecordsFiltered

        RETURN;
    END

    -- distric
    IF @key = 'district'
    BEGIN
        SELECT [name] = a.name
            , [value] = dbo.fn_guid_to_string(a.id)
            , icon = 'uni-icon'
            , icon_is = 0
            , isHtml = 1
        FROM [district] a
        WHERE (
                @search IS NULL
                OR a.name LIKE @search + '%'
                )
            AND (
                @refId IS NULL
                OR province_id = @refId
                )

        SET @RecordsFiltered = @@ROWCOUNT
        SET @RecordsTotal = @RecordsFiltered

        RETURN;
    END

    -----------------------------------------------
    -----------------------------------------------
    --default
    --DECLARE @prefix NVARCHAR(9) = 'dropdown_'
    --SET @key = @prefix + @key
    --config từ sys_config_data
    SELECT [name] = objName
        , [value] = objValue
        , icon = 'uni-icon'
        , icon_is = 0
        , isHtml = 1
    FROM [dbo].[fn_config_data_gets](@key) a
    ORDER BY a.intOrder

    SET @RecordsFiltered = @@ROWCOUNT
    SET @RecordsTotal = @RecordsFiltered
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(max)

    SET @ErrorNum = error_number()
    SET @ErrorMsg = error_message()
    SET @ErrorProc = error_procedure()
    SET @AddlInfo = ' '

    EXEC utl_ErrorLog_Set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , @key
        , 'GET'
        , @SessionID
        , @AddlInfo
END CATCH

GO

