
-- =============================================
-- Author:		AnhTT
-- Create date: 2024-04-122
-- Description:	Add/update component
-- =============================================
CREATE PROCEDURE [dbo].[sp_product_component_set] @userId UNIQUEIDENTIFIER = NULL
    , @id UNIQUEIDENTIFIER = NULL
    , @module_id UNIQUEIDENTIFIER = NULL
    , @repository_id BIGINT = NULL
    , @code NVARCHAR(100)
    , @name NVARCHAR(250)
AS
BEGIN
    DECLARE @valid BIT = 1
    DECLARE @message NVARCHAR(100) = N''
    DECLARE @product_id UNIQUEIDENTIFIER = (
            SELECT product_id
            FROM product_module
            WHERE id = @module_id
            )

    BEGIN TRY
        IF @id IS NULL
        BEGIN
            SET @message = N'Thêm mới thành công'

            GOTO FINAL;
        END

        UPDATE deployment_product_component
        SET code = @code
            , name = @name
            , product_id = @product_id
            , module_id = @module_id
            , repository_id = @repository_id
            , updated_by = @userId
            , updated = GETDATE()
        WHERE id = @id

        SET @message = N'Cập nhậtthành công'
    END TRY

    BEGIN CATCH
        DECLARE @ErrorNum INT
            , @ErrorMsg VARCHAR(200)
            , @ErrorProc VARCHAR(50)
            , @SessionID INT
            , @AddlInfo VARCHAR(max)

        SET @ErrorNum = error_number()
        SET @ErrorMsg = 'sp_product_component_set ' + error_message()
        SET @ErrorProc = error_procedure()
        SET @AddlInfo = LOWER(@userId)
        SET @valid = 0
        SET @message = error_message()

        EXEC utl_ErrorLog_Set @ErrorNum
            , @ErrorMsg
            , @ErrorProc
            , 'User'
            , 'Set'
            , @SessionID
            , @AddlInfo
    END CATCH

    FINAL:

    SELECT @valid AS valid
        , @message AS [messages]
        , @id id
END

GO

