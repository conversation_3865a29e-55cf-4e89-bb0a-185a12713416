CREATE PROCEDURE [dbo].[sp_app_account_info_set]
      @userId nvarchar(50),
	  @full_name nvarchar(50),
	  @email nvarchar(50),
	  @avatar nvarchar(350),
	  @sex int,
	  @address nvarchar(50),
	  @birth_day datetime
AS
BEGIN TRY
   DECLARE @valid BIT = 0;
   DECLARE @messages NVARCHAR(255);
   IF EXISTS (SELECT 1 FROM [User] WHERE userId = @userId)
		BEGIN
			update [User]
			set fullname = @full_name,
			    sex = @sex,
				email = @email,
				avatar = @avatar,
				address = @address
				--birth_day = @birth_day
			where userId = @userId

		END

	SET @valid = 1;
	SET @messages = N'Cập nhật dữ liệu tài khoản thành công'

	select @valid as valid, @messages as messages
END TRY
BEGIN CATCH
      DECLARE @ErrorNum INT
             ,@ErrorMsg VARCHAR(200)
             ,@ErrorProc VARCHAR(50)
             ,@SessionID INT
             ,@AddlInfo VARCHAR(MAX)

      SET @ErrorNum = ERROR_NUMBER()
      SET @ErrorMsg = 'sp_ode_account_info_set ' + ERROR_MESSAGE()
      SET @ErrorProc = ERROR_PROCEDURE()
      SET @AddlInfo = ''

      exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'profile', 'GET', @SessionID, @AddlInfo
END CATCH

GO

