

CREATE procedure [dbo].[sp_user_role_fields]
	@reportId	nvarchar(450),
	@userId		uniqueidentifier,
	@webRoleId	uniqueidentifier = null,
	@userRoleId uniqueidentifier = null

as
	begin try
		declare @tableKey nvarchar(100) = 'client_role_user'
		--1 user
		--if @UserId is not null and not exists(select 1 from client_users where UserId = @UserId) set @UserId = null 

		select @userId as userId
			  ,@webRoleId as webRoleId
			  ,@tableKey as tableKey
			  ,@userRoleId as userRoleId

		--2 group
			SELECT  objvalue as group_cd
					,objname as group_name 
			FROM [dbo].fn_config_data_gets ('user_role_group') 
		   order by intOrder
		
		--3 field
		SELECT [table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			  ,isnull(case [data_type] 
				  when 'nvarchar' then convert(nvarchar(350), 
					case [field_name] 
						when 'creationBy' then cr.fullName
						when 'auth_by' then au.fullName
						when 'webId' then cast(b.webId as varchar(50))
                        when 'orgId' then lower(cast(b.orgId as nvarchar(50)))
                        when 'orgName' then cast(b.orgName as nvarchar(50))
                        when 'companyId' then lower(cast(b.companyId as nvarchar(50)))
                        when 'companyName' then cast(b.companyName as nvarchar(50))
						when 'userId' then cast(isnull(b.userId,@userId) as varchar(50))
						when 'webRoleId' then cast(isnull(b.webRoleId,@webRoleId) as varchar(100))
						when 'userRoleId' then cast(b.userRoleId as varchar(100))
						--when 'channel' then b.channel
                        --when 'apikey' then wb.apiKey
					end) 
				  when 'datetime' then 
					case [field_name] 
						when 'creationTime' then format(b.creationTime,'dd/MM/yyyy HH:mm:ss')
						when 'auth_dt' then format(b.auth_dt,'dd/MM/yyyy HH:mm:ss')
					end  
				  else convert(nvarchar(50),
					case [field_name] 
						when 'work_st' then isnull(b.work_st,0)
					end) 
					end,[columnDefault])as columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject] = (case when field_name ='webRoleId' then (case when b.webId is not null then  '/api/v1/webrole/GetClientWebRoleList?webId=' + lower(cast(b.webId as nvarchar(50))) else columnObject end)
                                     when field_name = 'companyId' then (case when b.orgId is not null then '/api/v1/userrole/GetCompanies?orgId=' + lower(cast(b.orgId as nvarchar(50))) else columnObject end) else columnObject end)

			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
              ,columnDisplay
		  FROM (select * from sys_config_form a
			where table_name = @tableKey
				and (isvisiable = 1 or isRequire = 1)
				and view_type = 0) a
			left join client_role_user b on (b.UserId = @UserId and b.webRoleId = @webRoleId or userRoleId = @userRoleId)
            left join client_webs wb on b.webId = wb.webId and b.userId = @userId
			left join client_users cr on b.creationBy = cr.userId
			left join client_users au on b.auth_by = au.userId
		  order by ordinal


	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_Fields ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'Manage_Fields', 'GET', @SessionID, @AddlInfo
	end catch

GO

