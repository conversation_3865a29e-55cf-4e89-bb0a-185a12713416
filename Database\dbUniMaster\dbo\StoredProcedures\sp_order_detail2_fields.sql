CREATE PROCEDURE [dbo].[sp_order_detail2_fields]
    @userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    --
    IF @id IS NOT NULL
       AND NOT EXISTS
    (
        SELECT 1
        FROM [dbo].order_details
        WHERE id = @id
    )
        SET @id = NULL;
    --begin
    --1 thong tin chung
    SELECT @id [id];
    --2- cac group
    SELECT 1 [group_cd],
           N'Thông tin chung' [group_name];
    --3 tung o trong group
    --exec sp_get_data_fields '279E2777-0C0B-4CC0-BC16-01A177AA408E','order_details'
    IF EXISTS (SELECT 1 FROM dbo.order_details WHERE id = @id)
    BEGIN
        SELECT 
			   [table_name],
               [field_name],
               [view_type],
               [data_type],
               [ordinal],
               [columnLabel],
               [group_cd],
               ISNULL(   CASE [field_name]
                             WHEN 'prod_id' THEN
                                 CONVERT(NVARCHAR(500), ord_d.prod_id)
                             WHEN 'package_id' THEN
                                 CONVERT(NVARCHAR(500), ord_d.package_id)
                             WHEN 'start_dt' THEN
                                 FORMAT(ord_d.start_dt, 'dd/MM/yyyy')
                             WHEN 'end_dt' THEN
                                 FORMAT(ord_d.end_dt, 'dd/MM/yyyy')
                             WHEN 'type' THEN
                                 LOWER(CONVERT(NVARCHAR(500), ord_d.type))
                             WHEN 'amount' THEN
                                 LOWER(CONVERT(NVARCHAR(500), ord_d.amount))
                             WHEN 'userName' THEN
                                 LOWER(CONVERT(NVARCHAR(500), ord_a.login_uni_id))
                             WHEN 'password' THEN
                                 CONVERT(NVARCHAR(500), ord_a.login_uni_pass)
                             WHEN 'assign' THEN
                                 CONVERT(NVARCHAR(500),
                                         STUFF(
                                         (
                                             SELECT ',' + CONVERT(NVARCHAR(500),c.id)
                                             FROM dbo.curator c
                                                 LEFT JOIN dbo.orderDetails_curator od_c
                                                     ON od_c.curator_id = c.id
                                                 LEFT JOIN dbo.order_details od
                                                     ON od.id = od_c.ordDetail_id
                                             WHERE od.id = '279E2777-0C0B-4CC0-BC16-01A177AA408E' 
                                             FOR XML PATH('')
                                         ),
                                         1,
                                         1,
                                         ''
                                              )
                                        )
                         END,
                         [columnDefault]
                     ) AS columnValue,
               [columnClass],
               [columnType],
               [columnObject],
               [isSpecial],
               [isRequire],
               [isDisable],
               [isVisiable],
               NULL AS [IsEmpty],
               ISNULL(s.columnTooltip, s.[columnLabel]) AS columnTooltip
        FROM sys_config_form s
            INNER JOIN dbo.order_details ord_d
                ON ord_d.id = @id
            LEFT JOIN dbo.order_account ord_a
                ON ord_a.ordDetails_id = ord_d.id
        WHERE table_name = 'order_details'
        --AND s.field_name IN ('name','priority','project_Id','area_Id','basin_Id','jobDescription','jobType_Id','repeatWork','book_Id','startDate','dueDate','assign','conditionDescription','reportImages') 
        ORDER BY ordinal;
    END;
    ELSE
    BEGIN
        SELECT [id],
               [table_name],
               [field_name],
               [view_type],
               [data_type],
               [ordinal],
               [columnLabel],
               group_cd,
               a.columnDefault AS columnValue,
               [columnClass],
               [columnType],
               [columnObject],
               [isSpecial],
               [isRequire],
               [isDisable] = 0,
               [isVisiable],
               --,[IsEmpty]
               ISNULL(a.columnTooltip, a.[columnLabel]) AS columnTooltip
        --,case when @action = 'new' then 1 else 0 end as isChange
        FROM sys_config_form a
        WHERE table_name = 'order_details'
        --AND a.field_name IN ('name','priority','project_Id','area_Id','basin_Id','jobDescription','jobType_Id','repeatWork','book_Id','startDate','dueDate','assign','conditionDescription','reportImages') 
        ORDER BY ordinal;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_details2_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order_details2',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

