
-- =============================================
-- Author:		AnhTT
-- Create date: 2024-04-122
-- Description:	Add/update module
-- =============================================
CREATE PROCEDURE [dbo].[sp_product_module_set] @userId UNIQUEIDENTIFIER = NULL
    , @id UNIQUEIDENTIFIER = NULL
    , @product_id UNIQUEIDENTIFIER = NULL
    , @code NVARCHAR(100)
    , @name NVARCHAR(250)
AS
BEGIN
    DECLARE @valid BIT = 1
    DECLARE @message NVARCHAR(100) = N''

    BEGIN TRY
        IF @id IS NULL
        BEGIN
            SET @message = N'Thêm mới thành công'

            GOTO FINAL;
        END

        UPDATE product_module
        SET code = @code
            , name = @name
            , product_id = @product_id
            , updated_by = @userId
            , updated = GETDATE()
        WHERE id = @id

        SET @message = N'Cập nhậtthành công'
    END TRY

    BEGIN CATCH
        DECLARE @ErrorNum INT
            , @ErrorMsg VARCHAR(200)
            , @ErrorProc VARCHAR(50)
            , @SessionID INT
            , @AddlInfo VARCHAR(max)

        SET @ErrorNum = error_number()
        SET @ErrorMsg = 'sp_product_module_set ' + error_message()
        SET @ErrorProc = error_procedure()
        SET @AddlInfo = LOWER(@userId)
        SET @valid = 0
        SET @message = error_message()

        EXEC utl_ErrorLog_Set @ErrorNum
            , @ErrorMsg
            , @ErrorProc
            , 'product_module'
            , 'Set'
            , @SessionID
            , @AddlInfo
    END CATCH

    FINAL:

    SELECT @valid AS valid
        , @message AS [messages]
        , @id id
END

GO

