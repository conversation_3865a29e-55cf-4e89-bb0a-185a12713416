






CREATE procedure [dbo].[sp_user_role_close]
	 --@manId		uniqueidentifier
	 @userId	uniqueidentifier
	,@userRoleId	uniqueidentifier
as
begin 
	declare @valid int = 1
	declare @messages nvarchar(500) =N'Xóa quyền thành công'

	begin try		
		--if exists (select 1 from client_role_user where userId = @userId 
		--	and webRoleId = @webRoleId
		--	and work_st = 2
		--	)
		--begin
		--	set @valid = 0
		--	set @messages = N'Nhóm quyền đã được sử dụng ! Không được phép xóa'
		--	goto FINAL
		--end

		Update t set work_st = 3
		FROM client_role_user t
		WHERE userRoleId = @userRoleId
			and work_st = 2

		
	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_user_role_del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@User ' 

		exec utl_ErrorLog_set @ErrorNum, @ErrorMsg, @ErrorProc, 'User_Manage', 'Del', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as valid, @messages as [messages]

		select u.userId
			  ,u.fullName
			  ,u.loginName as userName
			  ,a.channel
			  ,w.mod_cd as prod_type
			  ,active = case when a.work_st = 2 then 1 else 0 end
		FROM client_role_user a 			
			join client_users u on a.userId = u.userId
			join client_web_role r on a.webRoleId = r.webRoleId
			join client_webs w on r.webId = w.webId
			where a.userRoleId = @userRoleId
				and a.work_st = 3

end

GO

