CREATE procedure [dbo].[sp_app_account_regset]
	@phone		nvarchar(20),
	@code		nvarchar(50)=null,
    @userId nvarchar(50) = null

as
begin
	declare @valid bit = 1
	declare @mess nvarchar(100) = N'Verify'
	begin try	

		UPDATE [dbo].[User]
		   SET last_login = getdate()
			  ,userId = lower(@userId)
			  --,is_verify = 1
			  ,verify_otp = 1
              ,is_verify = 1
		 WHERE phone =  @phone
		
		SELECT username as loginName
			  ,0 as IsCreatePassword
		FROM [dbo].[User]
		WHERE phone = @phone


	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_app_account_regset ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserAuth '  + @phone 
		set @valid = 0
		set @mess = error_message()

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'User', 'Set', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as valid
		  ,@mess as [messages]


end

GO

