









CREATE procedure [dbo].[sp_user_role_page]
	@userId			uniqueidentifier,
	@filter			nvarchar(250),
	@manageId		uniqueidentifier,
	@gridWidth			int				= 0,
	@Offset				int				= 0,
	@PageSize			int				= 10,
	@Total				int out,
	@TotalFiltered		int out,
	@Grid<PERSON>ey		nvarchar(100) out

as
	begin try
		
		set		@Offset					= isnull(@Offset, 0)
		set		@PageSize				= isnull(@PageSize, 10)
		set		@Total					= isnull(@Total, 0)
		set		@filter					= isnull(@filter,'')
		
		set		@GridKey				= 'view_user_role_page'

		if		@PageSize	= 0			set @PageSize	= 10
		if		@Offset		< 0			set @Offset		=  0
						

		select	@Total					= count(a.WebRoleId)
			FROM client_role_user a
				join client_web_role b on a.webRoleId = b.webRoleId
				join client_webs c on a.webId = c.webId
			WHERE a.UserId = @manageId 

		set @TotalFiltered = @Total

		if @Offset = 0
		begin
			select * from dbo.fn_config_list_gets (@GridKey, @gridWidth - 100) 
			order by [ordinal]
		end

		--
			SELECT a.webRoleId 
				  ,a.webId
				  ,a.userId
				  ,b.isAdmin 
				  ,b.roleName 
				  ,c.webName
				  ,creationTime	= format(a.creationTime ,'dd/MM/yyyy hh:mm:ss ttt')
				  ,creationBy	= r.fullName 
				  ,auth_dt		= format(a.auth_dt ,'dd/MM/yyyy hh:mm:ss ttt')
				  ,auth_by		= au.fullName
				  ,cast(userRoleId as nvarchar(50)) as userRoleId
				  ,work_status	= st.objValue1
				  ,a.channel
                  ,a.orgName
                  ,a.companyName
			FROM client_role_user a
				join client_web_role b on a.webRoleId = b.webRoleId
				join client_webs c on a.webId = c.webId
				left join client_users r on a.creationBy = r.userId
				left join client_users au on a.auth_by = au.userId
				left join [dbo].[fn_config_data_gets] ('work_process_status') st on isnull(a.work_st,0) = st.[objvalue]
			WHERE a.UserId = @manageId 
				--and (@filter = '' or a.loginName = @filter or a.phone = @filter)
			ORDER BY a.creationTime 
				  offset @Offset rows	
					fetch next @PageSize rows only
		
		

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_Page ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'profile', 'GET', @SessionID, @AddlInfo
	end catch

GO

