-- =============================================
-- Author:		AnhTT
-- Create date: 
-- Description:	<PERSON><PERSON><PERSON> thông tin tư vấn sang khách hàng
-- =============================================
CREATE PROCEDURE [dbo].[sp_customer_consultancy_registration_transfer] @userId NVARCHAR(50)
    , @id UNIQUEIDENTIFIER
AS
BEGIN TRY
    DECLARE @valid BIT = 0
        , @messages NVARCHAR(250) = N'Chuyển thông tin khách hàng thành công';
    DECLARE @customer_id UNIQUEIDENTIFIER

    SELECT @customer_id = id
    FROM customer
    WHERE consultancy_id = @id

    IF @customer_id IS NULL
    BEGIN
        SET @customer_id = NEWID()

        DECLARE @national_id UNIQUEIDENTIFIER

        SELECT TOP 1 @national_id = id
        FROM [national]
        WHERE code = '84'

        -- insert
        INSERT INTO customer (
            [id]
            , [full_name]
            , [postion]
            , [phone_1]
            , [email_1]
            , [org_name]
            , [org_address]
            , [description]
            , consultancy_id
            , created_by
            )
        SELECT @customer_id
            , [Fullname]
            , [Position]
            , [Phone]
            , [Email]
            , [CompanyName]
            , [CompanyAddress]
            , N'Thêm từ thông thông tin đăng ký tư vấn'
            , id
            , @userId
        FROM customer_consultancy_registration
        WHERE id = @id
    END

    FINAL:

    SELECT @valid valid
        , @messages AS [messages]
        , [id] = @customer_id;
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'customer'
        , 'SET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

