CREATE PROCEDURE [dbo].[sp_customer_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
	--@cust_group_id NVARCHAR(450) = NULL,
	@province_id NVARCHAR(450) = NULL,
	@district_id NVARCHAR(450) = NULL,
    @birth_day DATETIME = NULL,
    @sex INT = NULL,
    @cust_type INT = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT,
    @gridKey			nvarchar(100) = '' out
AS
BEGIN TRY
    set @gridKey = 'view_customers_page'
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(c.id)
    FROM dbo.customer c
    WHERE @filter = '' or c.full_name LIKE N'%' + @filter + '%' 
          

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets(@gridKey, 0)
            ORDER BY [ordinal];
    END;
    -- Data
	SELECT a.*
	FROM dbo.customer a
	--LEFT JOIN dbo.customer_group cg ON cg.id = c.cust_group_id
	WHERE @filter = '' or a.full_name LIKE N'%' + @filter + '%' 
	ORDER BY a.created  DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'customer',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

