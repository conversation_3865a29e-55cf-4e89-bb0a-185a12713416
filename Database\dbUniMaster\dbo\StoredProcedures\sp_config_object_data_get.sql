








CREATE procedure [dbo].[sp_config_object_data_get]
	@UserID	nvarchar(450),
	@objKey nvarchar(50)
as
	begin try		
		SELECT objvalue as value,objName as name FROM [dbo].fn_config_data_gets (@obj<PERSON>ey)
		order by intOrder

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_config_object_data_get ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'CustObj', 'Get', @SessionID, @AddlInfo
	end catch

GO

