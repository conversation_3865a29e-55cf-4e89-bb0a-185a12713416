


-- =============================================
-- Author:		<EMAIL>
-- Description: Thêm menu cho web
-- =============================================
CREATE procedure [dbo].[sp_client_action_del]
	@userId		nvarchar(450),
	@actionId	uniqueidentifier,
	@webId		uniqueidentifier
as
begin
	declare @valid int = 0
	declare @messages nvarchar(500) =N'Xóa thành công'
	begin try

		if not exists (select 1 from client_menu_action a 
			where actionId = @actionId and exists(select 1 from client_web_menu where webId = @webId and menuId = a.menuId))
		begin
			set @messages = N'Nhóm quyền đã được sử dụng ! Không được phép xóa'
			set @valid = 0
			goto FINAL
		end

		begin transaction DeleteAction_tran
	    
		-- x<PERSON>a toàn bộ một nhóm quyền
				
		Delete a from client_menu_action a where actionId = @actionId and exists(select 1 from client_web_menu where webId = @webId and menuId = a.menuId)

		Delete from client_web_action where actionId = @actionId and webId = @webId

		set @valid = 1
 
	COMMIT TRAN DeleteAction_tran
	end try
	begin catch
	ROLLBACK TRAN DeleteAction_tran

		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Action_Del ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''
		set @valid = 0
		set @messages					= error_message()

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'sp_Client_Action_Del', 'del', @SessionID, @AddlInfo
	end catch


	FINAL:
		select @valid as valid, @messages as messages

	end

GO

