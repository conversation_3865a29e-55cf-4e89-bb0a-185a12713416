



CREATE procedure [dbo].[sp_client_action_page]
	@UserId			nvarchar(50),
	@webId			uniqueidentifier,
	@filter			nvarchar(100),
	@gridWidth		int		= 0,
	@Offset			int				= 0,
	@PageSize		int				= 50,
	@Total			int out,
	@TotalFiltered	int out,
	@gridKey		nvarchar(100) out

as
	begin try
		if @UserId is null set @UserId = '81739c5c-2ca0-4e0f-acab-63373ea8a34a'

		set		@Offset					= isnull(@Offset, 0)
		set		@PageSize				= isnull(@PageSize, 50)
		set		@Total					= isnull(@Total, 0)
		set		@filter					= isnull(@filter,'')

		if		@PageSize	= 0			set @PageSize	= 10
		if		@Offset		< 0			set @Offset		=  0
		set		@gridKey				= 'view_Client_Action_Page'

		select @Total	= count(a.[actionId])
				from [client_web_action] a
               where a.webId = @webId
				and a.[actionName] like '%' + @filter + '%'
				--and (exists(select 1 from client_web_user where WebId = a.[webId] and UserId = @UserId)
				--		or exists(select userid from client_users where userId = @userId and isSupper = 1)
				--		)
				
				set @TotalFiltered = @Total

				if @Offset = 0
				begin
					select * from dbo.fn_config_list_gets(@gridKey, @gridWidth) 
					order by [ordinal]
				end

		SELECT [actionId]
			  ,[actionCd]
			  ,[actionName]
			  ,[created_dt]
			  ,[webId]
		  FROM [client_web_action] a
		  WHERE webId = @webId
		  order by a.[created_dt] desc 
			offset @Offset rows fetch next @PageSize rows only

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Action_Page ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'ActionPage', 'Get', @SessionID, @AddlInfo
	end catch

GO

