
CREATE PROCEDURE [dbo].[sp_registry_image_get] @UserId NVARCHAR(50) = NULL
    , @productId UNIQUEIDENTIFIER
AS
BEGIN TRY
    DECLARE @host NVARCHAR(100)

    SELECT TOP 1 @host = host
    FROM registry_config
    WHERE active = 1

    --latest image
    SELECT productId = product_id
        , componentId = pc.id
        , projectId = p.id
        , repositoryId = r.id
        , artifactId = a.id
        , tagId = t.id
        , tag = t.name
        , [image] = @host + '/' + p.name + '/' + r.name + ':' + t.name
        , a.digest
        , pushed_date = t.push_time
    FROM deployment_product_component pc
    INNER JOIN registry_repository r
        ON pc.repository_id = r.id
    INNER JOIN registry_artifact a
        ON a.repository_id = r.id
    INNER JOIN registry_tag t
        ON t.artifact_id = a.id
    INNER JOIN (
        SELECT sa.repository_id
            , artifact_id = MAX(artifact_id)
            , tag_id = MAX(st.id)
        FROM registry_artifact sa
        INNER JOIN registry_tag st
            ON sa.id = st.artifact_id
        GROUP BY sa.repository_id
        ) li
        ON t.artifact_id = li.artifact_id
            AND li.tag_id = t.id
    INNER JOIN registry_project p
        ON r.project_id = p.id
    WHERE pc.product_id = @productId
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(max)

    SET @ErrorNum = error_number()
    SET @ErrorMsg = 'sp_registry_image_get ' + error_message()
    SET @ErrorProc = error_procedure()
    SET @AddlInfo = ''

    EXEC utl_Insert_ErrorLog @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'ClientWebs,ClientWebRoles'
        , 'Get'
        , @SessionID
        , @AddlInfo
END CATCH

GO

