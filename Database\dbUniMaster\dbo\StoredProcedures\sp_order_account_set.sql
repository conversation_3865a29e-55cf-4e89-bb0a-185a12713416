CREATE PROCEDURE [dbo].[sp_order_account_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @ord_id NVARCHAR(450),
    @login_uni_id NVARCHAR(500),
    @login_uni_pass NVARCHAR(500),
    @login_uni_alias NVARCHAR(500),
    @user_id NVARCHAR(450)
AS
BEGIN TRY

    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF (@id IS NULL)
    BEGIN
        SET @id = NEWID();
        -- insert
        INSERT INTO dbo.order_account
        (
            id,
            ord_id,
            login_uni_id,
            login_uni_pass,
            login_uni_alias,
            user_id,
            created,
            created_by
        )
        VALUES
        (   @id,              -- id - uniqueidentifier
            @ord_id,          -- ord_id - uniqueidentifier
            @login_uni_id,    -- login_uni_id - nvarchar(50)
            @login_uni_pass,  -- login_uni_pass - nvarchar(50)
            @login_uni_alias, -- login_uni_alias - nvarchar(50)
            @user_id,         -- user_id - uniqueidentifier
            GETDATE(),        -- created - datetime
            @user_id);
        --
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
        UPDATE dbo.order_account
        SET ord_id = @ord_id,
            login_uni_id = @login_uni_id,
            login_uni_pass = @login_uni_pass,
            login_uni_alias = @login_uni_alias,
            [user_id] = @user_id,
            updated_by = @userId,
            updated = GETDATE()
        WHERE id = @id;

        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_account_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order_account',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

