

CREATE procedure [dbo].[sp_client_menu_page]
	@UserId			nvarchar(500) = '81739c5c-2ca0-4e0f-acab-63373ea8a34a',
	@webId			uniqueidentifier,
	@filter			nvarchar(100),
	@gridWidth		int		= 0,
	@Offset			int				= 0,
	@PageSize		int				= 50,
	@Total			int out,
	@TotalFiltered	int out,
	@gridKey		nvarchar(100) out

as
	begin try
		if @UserId is null set @UserId = '81739c5c-2ca0-4e0f-acab-63373ea8a34a'

		set		@Offset					= isnull(@Offset, 0)
		set		@PageSize				= isnull(@PageSize, 50)
		set		@Total					= isnull(@Total, 0)
		set		@filter					= isnull(@filter,'')

		if		@PageSize	= 0			set @PageSize	= 10
		if		@Offset		< 0			set @Offset		=  0
		set		@gridKey				= 'view_Client_Web_Menu_Page'

		select @Total	= count(a.[menuId])
				from [client_web_menu] a
               where a.webId = @webId
				and a.[title] like '%' + @filter + '%'
				
				set @TotalFiltered = @Total

				if @Offset = 0
				begin
					select * from dbo.fn_config_list_gets(@gridKey, @gridWidth) 
					order by [ordinal]
				end
					
				SELECT [menuId]
					  ,[webId]
					  ,[title]
					  ,[path]
					  ,[icon]
					  ,[class]
					  ,[badge]
					  ,[badgeClass]
					  ,[isExternalLink]
					  ,[isNavHeader]
					  ,case when exists(select 1 from [client_web_menu] where webId = @webId and menuId = a.parentId) then [parentId] else null end as [parentId]
					  ,[intPos]
					  ,[created_Dt]
					  ,childCount = (select count(*) from [client_web_menu] where parentId = a.menuId)
				  FROM [client_web_menu] a
               where a.webId = @webId
				and ([parentId] is null or not exists(select 1 from [client_web_menu] t where t.webId = @webId and t.menuId = a.parentId))
				and a.[title] like '%' + @filter + '%'
			   order by a.[intPos] 

			   SELECT [menuId]
					  ,[webId]
					  ,[title]
					  ,[path]
					  ,[icon]
					  ,[class]
					  ,[badge]
					  ,[badgeClass]
					  ,[isExternalLink]
					  ,[isNavHeader]
					  ,case when exists(select 1 from [client_web_menu] where webId = @webId and menuId = a.parentId) then [parentId] else null end as [parentId]
					  ,[intPos]
					  ,[created_Dt]
					  ,childCount = (select count(*) from [client_web_menu] where parentId = a.menuId)
				  FROM [client_web_menu] a
               where a.webId = @webId
				and exists(select 1 from [client_web_menu] t where t.webId = @webId and t.menuId = a.parentId)
				and a.[title] like '%' + @filter + '%'
			   order by a.[intPos] 

			 SELECT b.[actionId]
				  ,b.[actionCd]
				  ,a.[menuId]
				  ,b.[actionName]
				  ,b.created_dt				  
			  FROM [client_menu_action] a 
				join [dbo].client_web_action b on a.[actionId] = b.[actionId]
			  WHERE b.webId = @webId

			

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'view_Client_Menu_Page ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ''

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'MenuPage', 'Get', @SessionID, @AddlInfo
	end catch

GO

