
CREATE PROCEDURE [dbo].[sp_customer_products_set] @userId NVARCHAR(50)
    , @id UNIQUEIDENTIFIER = NULL
    , @customer_id [uniqueidentifier] 
    , @product_id [uniqueidentifier]
    , @start_date NVARCHAR(20)
    , @expire_date NVARCHAR(20)
    , @status [tinyint]
    , @reference [uniqueidentifier] = NULL
    , @database_id [uniqueidentifier]
    , @note [nvarchar] (4000)
AS
BEGIN TRY
    DECLARE @valid BIT = 0
        , @messages NVARCHAR(250);

    IF NOT EXISTS (
            SELECT 1
            FROM customer_products
            WHERE customer_id = @customer_id
                AND product_id = @product_id
            )
    BEGIN
        -- insert
        INSERT INTO customer_products (
            [customer_id]
            , [product_id]
            , [start_date]
            , [expire_date]
            , [status]
            , [reference]
            , [database_id]
            , [note]
            , [created]
            , [created_by]
            )
        VALUES (
            @customer_id
            , @product_id
            , CONVERT(DATETIME,@start_date,103)
            , CONVERT(DATETIME,@expire_date,103)
            , @status
            , @reference
            , @database_id
            , @note
            , GETDATE()
            , @userId
            )

        --
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
        UPDATE customer_products
        SET [customer_id] = @customer_id
            , [product_id] = @product_id
            , [start_date] = CONVERT(DATETIME,@start_date,103)
            , [expire_date] = CONVERT(DATETIME,@expire_date,103)
            , [status] = @status
            , [reference] = @reference
            , [database_id] = @database_id
            , [note] = @note
            , updated_by = @userId
            , updated = GETDATE()
        WHERE customer_id = @customer_id
        AND product_id = @product_id

        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;

    FINAL:

    SELECT @valid valid
        , @messages AS [messages];
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_product_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'customer_products'
        , 'SET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

