CREATE FUNCTION [dbo].[fn_config_from_gets] (
    @table_name NVARCHAR(200) = ''
    , @acceptLanguage NVARCHAR(50) = 'en'
    )
RETURNS @tbl TABLE (
    [id] [bigint] NOT NULL
    , [table_name] [nvarchar](100) NOT NULL
    , [field_name] [nvarchar](100) NOT NULL
    , [view_type] [int] NOT NULL
    , [data_type] [nvarchar](50) NOT NULL
    , [ordinal] [int] NULL
    , [group_cd] [nvarchar](150) NULL
    , [columnLabel] [nvarchar](max) NULL
    , [columnTooltip] [nvarchar](300) NULL
    , [columnDefault] [nvarchar](max) NULL
    , [columnClass] [nvarchar](500) NULL
    , [columnType] [nvarchar](50) NULL
    , [columnObject] [nvarchar](500) NULL
    , [isVisiable] [bit] NULL
    , [isSpecial] [bit] NULL
    , [isRequire] [bit] NULL
    , [isDisable] [bit] NULL
    , [isEmpty] [bit] NULL
    , [columnDisplay] [nvarchar](300) NULL
    , [isIgnore] [bit] NULL
    )
AS
BEGIN
    --
    INSERT INTO @tbl
    SELECT a.[id]
        , [table_name]
        , [field_name]
        , [view_type]
        , [data_type]
        , [ordinal]
        , [group_cd]
        , [columnLabel] = a.[columnLabel]
        , [columnTooltip] = a.[columnTooltip]
        , [columnDefault]
        , [columnClass]
        , [columnType]
        , [columnObject]
        , [isVisiable]
        , [isSpecial]
        , [isRequire]
        , [isDisable]
        , [isEmpty]
        , [columnDisplay]
        , [isIgnore]
    FROM [sys_config_form](NOLOCK) a
    WHERE table_name = @table_name
    RETURN
END

GO

