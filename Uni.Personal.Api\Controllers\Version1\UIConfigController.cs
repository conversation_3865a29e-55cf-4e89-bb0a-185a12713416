using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class UIConfigController : UniController
    {
        private readonly IUIConfigService _uiConfigService;

        public UIConfigController(IUIConfigService uiConfigService)
        {
            _uiConfigService = uiConfigService;
        }

        /// <summary>
        /// Get paginated list of UI configurations
        /// </summary>
        /// <param name="query">Filter criteria for UI configurations</param>
        /// <returns>Paginated list of UI configurations</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] UIConfigFilterInput? query)
        {
            if (query == null)
            {
                return GetErrorResponse<CommonListPage>(ApiResult.Error, 12, "Invalid request data");
            }
            var result = await _uiConfigService.GetPageAsync(query);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get UI configuration information by ID
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>UI configuration information</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewOidInfo>> GetInfo([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<CommonViewOidInfo>(ApiResult.Error, 12, "UI configuration ID is required");
            }

            var result = await _uiConfigService.GetInfoAsync(oid);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Create or update UI configuration
        /// </summary>
        /// <param name="info">UI configuration information</param>
        /// <returns>Validation result with UI configuration ID</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate<Guid?>>> SetInfo([FromBody] CommonViewOidInfo? info)
        {
            if (info == null)
            {
                return GetErrorResponse<BaseValidate<Guid?>>(ApiResult.Error, 12, "Invalid request data");
            }

            var result = await _uiConfigService.SetInfoAsync(info);

            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Delete UI configuration
        /// </summary>
        /// <param name="oid">UI configuration ID</param>
        /// <returns>Validation result</returns>
        [HttpDelete]
        public async Task<BaseResponse<BaseValidate>> Delete([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 12, "UI configuration ID is required");
            }

            var result = await _uiConfigService.DeleteAsync(oid);

            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }
    }
}
