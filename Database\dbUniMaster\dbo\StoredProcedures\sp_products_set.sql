CREATE PROCEDURE [dbo].[sp_products_set]
     @userId		NVARCHAR(450) = NULL
    ,@id			uniqueidentifier = NULL
    ,@prod_name		nvarchar(500) = NULL
	,@prod_desc		nvarchar(500) = NULL
	,@prod_line_id	uniqueidentifier = NULL
	,@icon			NVARCHAR(MAX) = NULL
    ,@prod_code		nvarchar(50) = NULL
	,@int_ord		int = 0
	,@app_st		int = 0
	,@prod_category_id  uniqueidentifier = null
AS
begin
	DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);
BEGIN TRY

    

    IF (@id IS NULL) or not exists(select 1 from products where id = @id)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.products
		(	id,
		    prod_name,
		    prod_desc,
		    prod_line_id,
		    icon,
		    created_by,
		    created,
            prod_code
			,int_ord
			,app_st
			,prod_category_id
		)
		VALUES
		(   @id,
		    @prod_name,       -- prod_line_name - nvarchar(50)
		    @prod_desc,       -- prod_line_description - nvarchar(100)
		    @prod_line_id,      -- parent_id - uniqueidentifier
		    @icon,         -- int_ord - int
		    @userId,      -- created_by - uniqueidentifier
		    GETDATE(), -- created - datetime
            @prod_code
			,@int_ord
			,@app_st
			,@prod_category_id
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.products
		 SET prod_name			= @prod_name
			,prod_desc			= @prod_desc
			,prod_line_id		= @prod_line_id
			,icon				= @icon
			,updated_by			= @userId
			,updated			= GETDATE()
            ,prod_code			= @prod_code
			,int_ord			= @int_ord
			,app_st				= @app_st
			,prod_category_id	= @prod_category_id
        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    

END TRY
BEGIN CATCH
    --SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_products_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;
	set @valid = 0
	set @messages = error_message()

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'products',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

FINAL:
    SELECT @valid valid,
           @messages AS [messages];
end

GO

