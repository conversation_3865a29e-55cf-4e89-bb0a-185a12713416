
CREATE PROCEDURE [dbo].[sp_product_category_set]
    @userId NVARCHAR(450) = NULL,
    @id uniqueidentifier = NULL,
    @category_name nvarchar(100),
	@category_desc nvarchar(500)= null,
	@prod_line_id uniqueidentifier = null,
    @int_ord int = 0
	,@app_st int = 0
AS
begin
	DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

BEGIN TRY

    

    IF (@id IS NULL) or not exists(select 1 from product_category where id = @id)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.product_category
		(
			id,
		    category_name,
		    category_desc,
		    prod_line_id,
		    int_ord,
		    app_st,
		    created_by,
		    created
		)
		VALUES
		(   
		    @id,
			@category_name,       -- category_name - nvarchar(50)
		    @category_desc,       -- category_desc - nvarchar(100)
		    @prod_line_id,      -- prod_line_id - uniqueidentifier
		    @int_ord,         -- int_ord - int
		    @app_st,         -- level - int
		    @userId,      -- created_by - uniqueidentifier
		    GETDATE() -- created - datetime
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.product_category
		SET category_name = @category_name,
			category_desc = @category_desc,
			prod_line_id = @prod_line_id,
			int_ord = @int_ord,
			[app_st] = @app_st,
			--prod_line_cd = @prod_line_cd,
			updated_by = @userId,
			updated = GETDATE()

        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_category_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;
	set @valid = 0
	set @messages = error_message()

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_category',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

FINAL:
    SELECT @valid valid,
           @messages AS [messages];
end

GO

