CREATE PROCEDURE [dbo].[sp_contract_set]
    @userId NVARCHAR(450) = NULL,
    @id NVARCHAR(500) = NULL,
    @ord_id nvarchar(450) = NULL,
	@ord_dts_id nvarchar(450) = NULL,
	@cont_no NVARCHAR(50) = NULL,
	@cont_dt nvarchar(50) = NULL,
	@cont_status int = NULL,
	@cont_link NVARCHAR(2000) = NULL
AS
BEGIN TRY

    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    IF (@id IS NULL)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.contract
		(
		    id,
		    ord_id,
		    ord_dts_id,
		    cont_no,
		    cont_dt,
		    cont_status,
		    cont_link,
		    created,
		    created_by
		)
		VALUES
		(   @id,      -- id - uniqueidentifier
		    @ord_id,      -- ord_id - uniqueidentifier
		    @ord_dts_id,      -- ord_dts_id - uniqueidentifier
		    @cont_no,       -- cont_no - nvarchar(50)
		    CONVERT(DATETIME,@cont_dt,103), -- cont_dt - datetime
		    @cont_status,      -- cont_status - uniqueidentifier
		    @cont_link,       -- cont_link - nvarchar(2000)
		    GETDATE(), -- created - datetime
		    @userId
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.[contract]
		SET ord_id = @ord_id,
			ord_dts_id = @ord_dts_id,
			cont_no = @cont_no,
			cont_dt = CONVERT(DATETIME,@cont_dt,103),
			cont_status = @cont_status,
			cont_link = @cont_link,
			updated_by = @userId,
			updated = GETDATE()
        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_contract_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'contract',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

