CREATE TABLE [dbo].[products] (
    [id]               UNIQUEIDENTIFIER CONSTRAINT [DF_products_id] DEFAULT (newid()) NOT NULL,
    [prod_code]        NVARCHAR (50)    NULL,
    [prod_name]        NVARCHAR (150)   NULL,
    [prod_desc]        NVARCHAR (200)   NULL,
    [prod_line_id]     UNIQUEIDENTIFIER NULL,
    [icon]             NVARCHAR (250)   NULL,
    [realm_name]       NVARCHAR (100)   NULL,
    [client_id]        NVARCHAR (100)   NULL,
    [client_secret]    NVARCHAR (100)   NULL,
    [created]          DATETIME         NULL,
    [created_by]       UNIQUEIDENTIFIER NULL,
    [updated]          D<PERSON><PERSON><PERSON>E         NULL,
    [updated_by]       UNIQUEIDENTIFIER NULL,
    [int_ord]          INT              NULL,
    [app_st]           INT              NULL,
    [prod_category_id] UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_product] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_product_product_line] FOREIGN KEY ([prod_line_id]) REFERENCES [dbo].[product_line] ([id])
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Lưu thông tin sản phẩm(APP)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'products';


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'đường dẫn registry lưu image', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'products', @level2type = N'COLUMN', @level2name = N'created';


GO

