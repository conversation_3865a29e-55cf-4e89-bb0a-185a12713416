CREATE TABLE [dbo].[deployment_version] (
    [id]                            UNIQ<PERSON><PERSON>ENTIFIER CONSTRAINT [DF__deployment_v__id__0A1E72EE] DEFAULT (newid()) NOT NULL,
    [tenant_id]                     UNIQUEIDENTIFIER NULL,
    [image_id]                      UNI<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ER NULL,
    [customer_product_component_id] UNIQUEIDENTIFIER NULL,
    [status]                        NVARCHAR (100)   NULL,
    [version_name]                  NVARCHAR (50)    NULL,
    [version]                       INT              NULL,
    [is_current]                    BIT              NULL,
    [created]                       DATETIME         CONSTRAINT [DF__deploymen__creat__0CFADF99] DEFAULT (getdate()) NULL,
    [created_by]                    UN<PERSON>Q<PERSON><PERSON>ENTIFIER NULL,
    [updated]                       DATETIM<PERSON>         NULL,
    [updated_by]                    UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK__deployme__3213E83FAF68B1A8] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_deployment_version_deployment_image] FOREIGN KEY ([image_id]) REFERENCES [dbo].[deployment_image] ([id]),
    CONSTRAINT [FK_deployment_version_deployment_tenant] FOREIGN KEY ([tenant_id]) REFERENCES [dbo].[deployment_tenant] ([id])
);


GO

