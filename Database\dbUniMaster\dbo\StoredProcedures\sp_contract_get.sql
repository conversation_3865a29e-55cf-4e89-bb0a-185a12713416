CREATE PROCEDURE [dbo].[sp_contract_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(30) = NULL,
	@cust_id NVARCHAR(450) = NULL,

    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY

    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    --

    IF @PageSize = 0
        SET @PageSize = 10;
    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(c.id)
    FROM dbo.[contract] c
	LEFT JOIN dbo.[order_details] od ON od.id = c.ord_dts_id
	LEFT JOIN dbo.[order] o ON o.id = od.ord_id
	WHERE dbo.ufn_removeMark(c.cont_no) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		AND (@cust_id IS NULL OR @cust_id = o.cust_id)
          

    SET @TotalFiltered = @Total;

    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END;
    IF @Offset = 0
    BEGIN
            SELECT *
            FROM [dbo].fn_config_list_gets('view_contract_page', 0)
            ORDER BY [ordinal];
    END;
    -- Data
	SELECT c.id,o.cust_id,c.ord_id,c.ord_dts_id,c.cont_no,cont_dt = FORMAT(c.cont_dt, 'dd/MM/yyyy'), cd.value1 AS contractStatusText,cd1.value1 AS orderStatusText,cd2.value1 AS orderStatusPayText,o.ord_no + '_' + o.ord_name AS ord_noName,od.start_dt, od.end_dt,o.ord_status,o.ord_status_pay
    FROM dbo.[contract] c
	LEFT JOIN dbo.[order_details] od ON od.id = c.ord_dts_id
	LEFT JOIN dbo.[order] o ON o.id = od.ord_id
	LEFT JOIN dbo.sys_config_data cd ON cd.key_1 = 'cont_status' AND cd.key_2 = c.cont_status
	LEFT JOIN dbo.sys_config_data cd1 ON cd1.key_1 = 'ord_status' AND cd1.key_2 = o.ord_status
	LEFT JOIN dbo.sys_config_data cd2 ON cd2.key_1 = 'ord_status_pay' AND cd2.key_2 = o.ord_status_pay
	WHERE dbo.ufn_removeMark(c.cont_no) LIKE N'%' + dbo.ufn_removeMark(@filter) + '%' 
		AND (@cust_id IS NULL OR @cust_id = o.cust_id)
	ORDER BY CASE
                 WHEN c.updated > c.created THEN
                     c.updated
                 ELSE
                     c.created
             END DESC,
             c.cont_no DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_contract_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'contract',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

