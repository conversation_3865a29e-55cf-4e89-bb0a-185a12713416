
-- =============================================
-- Author: AnhTT
-- Create date: 2025-04-23
-- Description:	<PERSON><PERSON> s<PERSON>ch sản phẩm KH sử dụng
-- Output: 
-- =============================================
CREATE PROCEDURE [dbo].[sp_customer_products_page] @userId NVARCHAR(50) = NULL
    , @customerId UNIQUEIDENTIFIER = NULL
    , @filter NVARCHAR(100) = NULL
    , @gridWidth INT = NULL
    , @Offset INT = 0
    , @PageSize INT = 10
    , @Total INT = 0 OUT
    , @gridKey NVARCHAR(50) OUT
    , @TotalFiltered INT = 0 OUT
AS
BEGIN TRY
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    --
    IF @PageSize = 0
        SET @PageSize = 10;

    IF @Offset < 0
        SET @Offset = 0;

    SELECT @total = COUNT(1)
    FROM customer t

    SET @TotalFiltered = @Total;

    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets('view_customer_products_page', 0)
        ORDER BY [ordinal];
    END;

    -- Data
    SELECT [a].[id]
        , [a].[customer_id]
        , a.product_id
        , [product_name] = p.prod_name
        , [a].[start_date]
        , [a].[expire_date]
        , [a].[status]
        , [a].[reference]
        , [a].[database_id]
        , [a].[note]
        , [a].[created]
        , [a].[created_by]
        , [a].[updated]
        , [a].[updated_by]
    FROM customer_products a
    LEFT JOIN products p ON a.product_id = p.id
    -- LEFT JOIN [dbo].[fn_config_data_gets]('cust_type') t ON a.cust_type = t.objValue
    -- LEFT JOIN [dbo].[fn_config_data_gets]('sex') g ON a.sex = g.objValue
    WHERE customer_id = @customerId
    ORDER BY created DESC OFFSET @Offset ROWS

    FETCH NEXT @PageSize ROWS ONLY;
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_customer_products_page ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'customer_products'
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;

GO

