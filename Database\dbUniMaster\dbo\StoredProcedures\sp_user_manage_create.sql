










CREATE procedure [dbo].[sp_user_manage_create]
	 @userId		nvarchar(450) ='81739c5c-2ca0-4e0f-acab-63373ea8a34a'
	,@id			uniqueidentifier ='558ff16f-baeb-43df-980c-41688bc0f205'
	,@username		nvarchar(200) ='camanhnguyen'
	,@firstName		nvarchar(200) = null
	,@lastName		nvarchar(200) = null
	,@email				nvarchar(200) = null
	,@emailVerified		nvarchar(200) = null
	,@federationLink	nvarchar(200) = null
	,@origin			nvarchar(200) = null
	,@self				nvarchar(200) = null
	,@serviceAccountClientId	nvarchar(200) = null

as
	begin try
		declare @tableKey nvarchar(100) = 'client_users'
		--1 user
		--if @id is not null and not exists(select 1 from client_users where UserId = @id) set @id = null 

		select @id as gd
			  ,@tableKey as tableKey

		--2 group
			SELECT  objvalue as group_cd
					,objname as group_name 
			FROM [dbo].fn_config_data_gets ('common_group') 
		   order by intOrder
		
		--3 field
		SELECT [table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			  ,isnull(case [data_type] 
				  when 'nvarchar' then convert(nvarchar(350), 
					case [field_name] 
						when 'avatarUrl' then isnull(b.avatarUrl ,@federationLink)
						when 'loginName' then isnull(b.loginName,@username)
						when 'fullName' then isnull(b.fullName, @firstName + ' ' + @lastName)
						when 'phone' then b.phone
						when 'email' then isnull(b.email,@email)
						when 'position' then isnull(b.position,@origin)
						when 'userId' then cast(@id as nvarchar(100))
					end) 
				  when 'datetime' then convert(nvarchar(10),
					case [field_name] 
						when 'auth1_dt' then b.auth1_dt 
						when 'created_Dt' then b.created_Dt 
					end ,103) 
				  else convert(nvarchar(50),
					case [field_name] 
						when 'isSupper' then b.isSupper
					    when 'auth1_st' then b.auth1_st  
						when 'auth2_st' then b.auth2_st
						when 'lock_st' then b.lock_st 
						when 'userRoleType' then b.userRoleType
					end) end,[columnDefault])as columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject]
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
		  FROM (select * from sys_config_form a
			where table_name = @tableKey
				and (isvisiable = 1 or isRequire = 1)
				and view_type = 0) a
			left join client_users b on b.UserId = @id
		  order by ordinal

		  	--6 role
			SELECT a.WebRoleId
				  ,a.WebId
				  ,a.UserId
				  ,b.isAdmin 
				  ,b.RoleName 
				  ,c.webName
			FROM client_role_user a
				join client_web_role b on a.webRoleId = b.webRoleId
				join client_webs c on a.webId = c.webId
			WHERE a.UserId = @UserId 
				--and a.webId = @webId

			

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_COR_User_Manage_createe ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'Manage_Fields', 'GET', @SessionID, @AddlInfo
	end catch

GO

