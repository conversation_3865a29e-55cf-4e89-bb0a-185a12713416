create PROCEDURE [dbo].[sp_sys_manager_filter_get]
	@userId nvarchar(50) = null,
    @table_key nvarchar(50) = null
	
AS
	BEGIN TRY
		 
		 SELECT id = null
			 ,tableKey = @table_key
		     ,groupKey = 'common_group_info'
		

		SELECT *
		FROM [dbo].[fn_get_field_group] ('common_group_info') 
		order by intOrder

		--2 tung o 
		SELECT [id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel] = [columnLabel] 
			  ,[group_cd]
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject]
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
		  FROM [sys_config_form] a
		  where a.table_name = @table_key 
		  and (isVisiable = 1 or isRequire =1)
		  order by ordinal

	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_sys_manager_filter_get ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= ' '

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'sp_sys_manager_filter_get', 'GET', @SessionID, @AddlInfo
	end catch

GO

