



CREATE procedure [dbo].[sp_client_role_fields]
	@UserId		nvarchar(450),
	@webRoleId	uniqueidentifier = null,
	@webId		uniqueidentifier = null

as
	begin try
	    declare @gridWidth int
		declare @tableKey nvarchar(100) = 'client_web_role'

		set @gridWidth = isnull(@gridWidth,0)

		if exists(select webRoleId from client_web_role where webRoleId = @webRoleId)
		begin
			--1--
			select webRoleId 
				  ,webId
				  ,@tableKey as tableKey
			  from client_web_role 
			  where webRoleId = @webRoleId
			--2--
			SELECT  objvalue as group_cd
					,objname as group_name 
			FROM [dbo].fn_config_data_gets ('common_group') 
			--3--
			select [table_name]
				  ,[field_name]
				  ,[view_type]
				  ,[data_type]
				  ,[ordinal]
				  ,[columnLabel]
				  ,[group_cd]
				    ,isnull(case [data_type] 
					  when 'nvarchar' then convert(nvarchar(451), 
						case [field_name] 
							  when 'roleCd' then b.roleCd
							  when 'roleName' then b.roleName
						end) 
					  when 'datetime' then convert(nvarchar(100),
						case [field_name] 
							when 'creationTime' then format(b.created_dt,'dd/MM/yyyy HH:mm:ss')
						end ,103) 
					  else convert(nvarchar(50),
						case [field_name] 
							when 'isAdmin' then b.isAdmin
						end) end,[columnDefault])as columnValue
				  ,[columnClass]
				  ,[columnType]
				  ,[columnObject]
				  ,[isSpecial]
				  ,[isRequire]
				  ,[isDisable]
				  ,[IsVisiable]
				  ,isnull(a.columnTooltip,[columnLabel]) as columnTooltip
			  FROM sys_config_form a
				  ,client_web_role b  
			  where a.table_name = @tableKey
			  and (isVisiable = 1 or isRequire =1)
			  and b.webRoleId = @webRoleId
			  order by ordinal
		end
		else
			begin
				--4--
				select @webId as webId
					  ,@tableKey as tableKey

				--5--
				SELECT  objvalue as group_cd
						,objname as group_name 
				FROM [dbo].fn_config_data_gets ('common_group') 
				--6--
				select [table_name]
					  ,[field_name]
					  ,[view_type]
					  ,[data_type]
					  ,[ordinal]
					  ,[columnLabel]
					  ,[group_cd]
					  ,[columnDefault] as [columnValue]
					  ,[columnClass]
					  ,[columnType]
					  ,[columnObject]
					  ,[isSpecial]
					  ,[isRequire]
					  ,[isDisable]
					  ,[IsVisiable]
					  ,isnull(a.columnTooltip,[columnLabel]) as columnTooltip
				  from (select * from sys_config_form 
						where table_name = @tableKey
							and (isVisiable = 1 or isRequire =1)) as a
				  order by a.ordinal
			end

			
			SELECT [menuRoleId]
				  ,a.[menuId]
				  ,c.[webId]
				  ,a.[webRoleId]				  
				  ,a.intPos
				  ,c.roleCd as roleCd
				  ,b.title as title
			  FROM [dbo].[client_role_menu] a 
				join client_web_menu b on a.menuId = b.menuId
				join client_web_role c on a.webRoleId = c.webRoleId
			 where a.webRoleId = @webRoleId
			 order by b.intPos

			 SELECT b.[actionId]
				  ,b.[actionCd]
				  ,a.[menuId]
				  ,b.[actionName]
				  ,b.created_dt			
				  ,c.menuRoleId
			  FROM [Client_Role_Action] a 
				join client_web_action b on a.[actionId] = b.[actionId]
				join [client_role_menu] c on a.menuRoleId = c.menuRoleId 
			  WHERE c.webRoleId = @webRoleId


	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_Client_Role_Fields ' + error_message()
		set @ErrorProc					= error_procedure()
		set @AddlInfo					= ' '

		exec utl_Insert_ErrorLog @ErrorNum, @ErrorMsg, @ErrorProc, 'WebMenu', 'GET', @SessionID, @AddlInfo
	
	end catch

GO

