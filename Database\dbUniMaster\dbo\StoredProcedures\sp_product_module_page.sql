
CREATE PROCEDURE [dbo].[sp_product_module_page] @userId NVARCHAR(450) = NULL
    , @productId UNIQUEIDENTIFIER = NULL
    , @filter NVARCHAR(30) = NULL
    , @Offset INT = 0
    , @PageSize INT = 10
    , @gridWidth INT = 0
    , @gridKey NVARCHAR(100) = NULL OUT
    , @Total INT = 0 OUT
    , @TotalFiltered INT = 0 OUT
AS
BEGIN TRY
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    --
    IF @PageSize < 0
    BEGIN
        SET @PageSize = 10;
    END

    IF @PageSize = 0
        SET @PageSize = 10;

    IF @Offset < 0
        SET @Offset = 0;

    SELECT @Total = COUNT(1)
    FROM product_module

    SET @TotalFiltered = @Total;

    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets('view_product_module_page', 0)
        ORDER BY [ordinal];
    END;

    -- Data
    SELECT *
    FROM product_module
    ORDER BY created DESC OFFSET @Offset ROWS

    FETCH NEXT @PageSize ROWS ONLY
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_module_page ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'product_module'
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH

GO

