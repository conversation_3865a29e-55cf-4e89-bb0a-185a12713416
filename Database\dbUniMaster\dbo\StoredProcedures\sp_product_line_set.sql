CREATE PROCEDURE [dbo].[sp_product_line_set]
    @userId NVARCHAR(450) = NULL,
    @id uniqueidentifier = NULL,
	@prod_line_cd nvarchar(100)=null,
    @prod_line_name nvarchar(100),
	@prod_line_desc nvarchar(500)= null,
	@parent_id uniqueidentifier = null,
    @int_ord int = 0,
	@level int = 0
AS
begin
	DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);
BEGIN TRY

    

    IF (@id IS NULL) or not exists(select 1 from product_line where id = @id)
    BEGIN
		SET @id = NEWID();
		-- insert
		INSERT INTO dbo.product_line
		(	id,
			prod_line_cd,
		    prod_line_name,
		    prod_line_desc,
		    parent_id,
		    int_ord,
		    level,
		    created_by,
		    created
		)
		VALUES
		(   @id,
		    @prod_line_cd,
			@prod_line_name,       -- prod_line_name - nvarchar(50)
		    @prod_line_desc,       -- prod_line_desc - nvarchar(100)
		    @parent_id,      -- parent_id - uniqueidentifier
		    @int_ord,         -- int_ord - int
		    @level,         -- level - int
		    @userId,      -- created_by - uniqueidentifier
		    GETDATE() -- created - datetime
		    )
		--
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END;
    ELSE
    BEGIN
		UPDATE dbo.product_line
		SET prod_line_name = @prod_line_name,
			prod_line_desc = @prod_line_desc,
			parent_id = @parent_id,
			int_ord = @int_ord,
			[level] = @level,
			prod_line_cd = @prod_line_cd,
			updated_by = @userId,
			updated = GETDATE()

        WHERE id = @id;
		
        --
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END;
    

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_line_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;
	set @valid = 0
	set @messages = error_message()

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_line',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;
FINAL:
    SELECT @valid valid,
           @messages AS [messages];
end

GO

