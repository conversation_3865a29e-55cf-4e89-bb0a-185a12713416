-- =============================================
-- Author:		AnhTT
-- Description: sync info form harbor
-- =============================================
CREATE PROCEDURE [dbo].[sp_registry_harbor_sync] @userId NVARCHAR(50)
    , @payload NVARCHAR(MAX) --json string
AS
BEGIN TRY
    --PUSH_ARTIFACT|DELETE_ARTIFACT
    DECLARE @type NVARCHAR(50)
    DECLARE @project_id BIGINT
    DECLARE @repository_id BIGINT
    DECLARE @project_name NVARCHAR(250)
    DECLARE @repository_name NVARCHAR(250)

    SELECT @type = JSON_VALUE(@payload, '$.type')

    --TODO: update deleted flag
    IF @type = 'DELETE_ARTIFACT'
    BEGIN
        --artifacts
        SELECT JSON_VALUE(value, '$.digest') AS Digest
            , JSON_VALUE(value, '$.tag') AS Tag
            , JSON_VALUE(value, '$.resource_url') AS ResourceUrl
        FROM OPENJSON(@payload, '$.event_data.resources')

        RETURN
    END

    --
    -- ADD OR UPDATE
    --
    DECLARE @artifact_id BIGINT
    DECLARE @digest CHAR(71)
        , @tag NVARCHAR(50)
        , @resource_url NVARCHAR(250)

    SELECT @project_name = JSON_VALUE(@payload, '$.event_data.repository.namespace')
        , @repository_name = JSON_VALUE(@payload, '$.event_data.repository.name')
        , @digest = JSON_VALUE(@payload, '$.event_data.resources[0].digest')
        , @tag = JSON_VALUE(@payload, '$.event_data.resources[0].tag')
        , @resource_url = JSON_VALUE(@payload, '$.event_data.resources[0].resource_url')

    IF @project_name IS NULL
        OR @repository_name IS NULL
        RETURN

    SELECT @project_id = id
    FROM registry_project
    WHERE name = @project_name

    SELECT @artifact_id = id
    FROM registry_artifact
    WHERE [digest] = @digest

    SELECT @repository_id = id
    FROM registry_repository
    WHERE name = @repository_name

    --project
    IF @project_id IS NULL
    BEGIN
        DECLARE @inserted_project_id TABLE (id BIGINT)

        INSERT INTO registry_project (name)
        OUTPUT inserted.id
        INTO @inserted_project_id
        VALUES (@project_name)

        SELECT TOP 1 @project_id = id
        FROM @inserted_project_id
    END

    --repository
    IF @repository_id IS NULL
    BEGIN
        DECLARE @inserted_repository_id TABLE (id BIGINT)

        INSERT INTO registry_repository (
            project_id
            , name
            )
        OUTPUT inserted.id
        INTO @inserted_repository_id
        VALUES (
            @project_id
            , @repository_name
            )

        SELECT TOP 1 @repository_id = id
        FROM @inserted_repository_id
    END

    --artifact
    IF @artifact_id IS NULL
    BEGIN
        DECLARE @inserted_artifact TABLE (id BIGINT)

        INSERT INTO registry_artifact (
            repository_id
            , digest
            )
        OUTPUT inserted.id
        INTO @inserted_artifact
        VALUES (
            @repository_id
            , @digest
            )

        SELECT TOP 1 @artifact_id = id
        FROM @inserted_artifact
    END

    --tag
    INSERT INTO registry_tag (
        artifact_id
        , name
        , resource_url
        )
    VALUES (
        @artifact_id
        , @tag
        , @resource_url
        )
END TRY

BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(max)

    SET @ErrorNum = error_number()
    SET @ErrorMsg = error_message()
    SET @ErrorProc = error_procedure()
    SET @AddlInfo = ''

    EXEC utl_Insert_ErrorLog @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'sp_registry_harbor_sync'
        , 'SET'
        , @SessionID
        , @AddlInfo
END CATCH

GO

