
CREATE PROCEDURE [dbo].[sp_get_data_fields] @id NVARCHAR(50)
    , @tableName NVARCHAR(100)
    , @table_key NVARCHAR(100) = 'id'
AS
BEGIN
    DECLARE @data_table NVARCHAR(100) = @tableName
    DECLARE @is_temp_table BIT = 0

    --temp table
    IF @tableName LIKE '#%'
    BEGIN
        SET @tableName = REPLACE(@tableName, '#', '')
        SET @is_temp_table = 1
    END

    IF (@id IS NULL
        OR @id = '')
        AND @is_temp_table = 0
    BEGIN
        SELECT [id]
            , [table_name]
            , [field_name]
            , [view_type]
            , [data_type]
            , [ordinal]
            , [group_cd]
            , [columnLabel]
            , [columnTooltip]
            , [columnClass]
            , [columnDisplay]
            , [columnType]
            , [columnObject]
            , [isVisiable]
            , [isSpecial]
            , [isRequire]
            , [isDisable]
            , [isIgnore]
            , columnDefault [columnValue]
        FROM sys_config_form
        WHERE table_name = @tableName
            AND isUsed = 1
        --and (isVisiable = 1 or isRequire = 1)
        ORDER BY ordinal
    END
    ELSE
    BEGIN
        IF ISNULL(@table_key, '') = ''
            SET @table_key = 'id'

        DECLARE @sql NVARCHAR(MAX) = N''
            , @tableCols NVARCHAR(max) = N''
            , @unpivotCols NVARCHAR(max) = N'' --build columns

        SELECT @tableCols = @tableCols + ',' + CASE 
                WHEN f.data_type = 'date'
                    THEN 'convert(nvarchar(max),' + QUOTENAME(f.field_name) + ',103) ' + QUOTENAME(f.field_name)
                WHEN f.data_type = 'datetime'
                    THEN 'CONVERT(NVARCHAR(MAX),format(' + QUOTENAME(f.field_name) + ',''dd/MM/yyyy hh:mm:ss'')) ' + QUOTENAME(f.field_name)
                WHEN f.data_type = 'uniqueidentifier'
                    THEN 'cast(dbo.fn_guid_to_string(' + QUOTENAME(f.field_name) + ') as nvarchar(max))' + QUOTENAME(f.field_name)
                WHEN f.data_type = 'bit'
                    OR f.columnType = 'checkbox'
                    THEN 'convert(nvarchar(max),case ' + QUOTENAME(f.field_name) + ' when 1 then ''True'' else ''False'' end) ' + QUOTENAME(f.field_name)
                ELSE 'convert(nvarchar(max),' + QUOTENAME(f.field_name) + ') ' + QUOTENAME(f.field_name)
                END
        FROM dbo.sys_config_form f
        WHERE table_name = @tableName
            AND f.isUsed = 1

        SET @tableCols = RIGHT(@tableCols, len(@tableCols) - 1) --unpivot cols

        SELECT @unpivotCols = @unpivotCols + ',' + QUOTENAME(f.field_name)
        FROM dbo.sys_config_form f
        WHERE f.table_name = @tableName
            AND f.isUsed = 1

        SET @unpivotCols = RIGHT(@unpivotCols, len(@unpivotCols) - 1) --build sql
        SET @sql = 'WITH tempTb as (SELECT * FROM (' + 'SELECT ' + @tableCols + ' FROM ' + QUOTENAME(@data_table) + IIF(@is_temp_table = 1, '', ' WHERE ' + @table_key + '=@id') + ')p UNPIVOT( columnValue FOR field_name in (' + @unpivotCols + ')) as unp) ' + 'SELECT f.[id]
                ,f.[table_name]
                ,f.[field_name]
                ,f.[view_type]
                ,f.[data_type]
                ,f.[ordinal]
                ,f.[group_cd]
                ,f.[columnLabel]
                ,f.[columnTooltip]
                ,f.[columnClass]
                ,f.[columnDisplay]
                ,f.[columnType]
                ,f.[columnObject]
                ,f.[isVisiable]
                ,f.[isSpecial]
                ,f.[isRequire]
				,f.[isDisable]
                , f.[isIgnore]
				,t.columnValue [columnValue]
				FROM sys_config_form f LEFT JOIN tempTb t ON f.field_name = t.field_name WHERE f.table_name = @tableName AND isUsed = 1 ORDER BY ordinal' --execute

        --PRINT @sql

        EXEC sp_executesql @sql
            , N'@id nvarchar(50), @tableName nvarchar(100)'
            , @id
            , @tableName
    END
END

GO

