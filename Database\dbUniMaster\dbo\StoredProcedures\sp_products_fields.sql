CREATE PROCEDURE [dbo].[sp_products_fields]
     @userId		NVARCHAR(50) = NULL
    ,@id			UNIQUEIDENTIFIER = NULL
	,@prod_line_id	UNIQUEIDENTIFIER = null
AS
BEGIN TRY
    --
    IF @id IS NOT NULL
       AND NOT EXISTS
    (
        SELECT 1
        FROM [dbo].products
        WHERE id = @id
    )
        SET @id = NULL;
    
	--begin
	SELECT *
	INTO #products
	FROM products
	WHERE id = @id
    --1 thong tin chung
    SELECT @id [id];
    --2- cac group
    SELECT  *
	FROM [dbo].fn_get_field_group('common_group') 
    --3 tung o trong group
    --exec sp_get_data_fields @id,'products'
	if @id IS NOT NULL
	begin

		SELECT a.[id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			 ,CASE [data_type] 
				  WHEN 'nvarchar' THEN CONVERT(NVARCHAR(350), CASE [field_name] 
                             WHEN 'prod_name' THEN b.prod_name
                             WHEN 'prod_desc' THEN b.prod_desc
							 when 'prod_code' then b.prod_code
							 when 'icon' then b.icon
						END) 
				  WHEN 'datetime' THEN CASE [field_name] 
							WHEN 'created' THEN FORMAT(b.created,'dd/MM/yyyy') END
                            WHEN 'updated' THEN FORMAT(b.updated,'dd/MM/yyyy')
                 
				 WHEN 'uniqueidentifier' THEN convert(NVARCHAR(350), CASE [field_name] 
							WHEN 'id' THEN (cast(b.id AS VARCHAR(50))) 
                            WHEN 'prod_line_id' THEN (cast(b.prod_line_id AS VARCHAR(50))) 
                            WHEN 'prod_category_id' THEN (cast(b.prod_category_id AS VARCHAR(50))) 	
					  END)
                ELSE convert(NVARCHAR(50), CASE [field_name] 
					  WHEN 'int_ord' THEN b.int_ord
					  WHEN 'app_st' THEN b.app_st
					  END)
				END AS columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject] = case when field_name = 'prod_category_id' then [columnObject] + lower(cast(b.prod_line_id as varchar(100))) else [columnObject] end
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM #products b
			OUTER APPLY
				(
					SELECT *
					FROM sys_config_form
					WHERE table_name = 'products' 
				)a
		  where 
			(isvisiable = 1 or isRequire = 1)
		  order by ordinal
	
	end
	else
	begin
		
		SELECT [id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,group_cd
			  ,case when a.field_name ='prod_line_id' then cast(@prod_line_id as nvarchar(50))
					else columnDefault end as columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject] = case when field_name = 'prod_category_id' then [columnObject] + lower(cast(@prod_line_id as varchar(100))) else [columnObject] end
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM sys_config_form a
		  where table_name = 'products' 
		  and (isvisiable = 1 or isRequire = 1)
		  order by ordinal

		--2
	end

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_products_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'Products',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

