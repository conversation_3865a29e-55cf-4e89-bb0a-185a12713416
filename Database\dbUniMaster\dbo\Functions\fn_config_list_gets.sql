




CREATE  FUNCTION [dbo].[fn_config_list_gets]
(
	@view_grid nvarchar(50),
	@grid_width int
)
RETURNS @tbl TABLE
(
	 [columnField] nvarchar(50)
	,[columnCaption] nvarchar(50)
	,[columnWidth] int
	,[fieldType] nvarchar(50)
	,[columnClass] nvarchar(300)
	,[columnCondition] nvarchar(400)
	,[Pinned] nvarchar(50)
	,[isMasterDetail] bit
	,[isStatusLable] bit
	,[isHide] bit
	,[isFilter] bit
	,[ordinal] int	index idx clustered
)
AS

BEGIN
--
	declare @total_width int
	declare @rt float 
	set @total_width = (select sum(isnull(columnWidth,100)) from [dbo].sys_config_list where view_grid = @view_grid and isHide = 0)
	if @grid_width > @total_width
		set @rt = cast(@grid_width as float)/@total_width
	else
		set @rt = 1

	Insert into @tbl 
	  SELECT [columnField]
			,[columnCaption]
			,@rt * isnull([columnWidth],100) as columnWidth
			,[fieldType]
			,[cellClass] as columnClass
			,[conditionClass] as columnCondition
			,[Pinned]
			,[isMasterDetail]
			,[isStatusLable]
			,[isHide]
			,[isFilter]
			,[ordinal]
		FROM [dbo].sys_config_list cc
		where view_grid = @view_grid 
	return 
END

GO

