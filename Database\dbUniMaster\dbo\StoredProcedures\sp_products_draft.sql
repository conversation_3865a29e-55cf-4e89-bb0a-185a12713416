
CREATE PROCEDURE [dbo].[sp_products_draft]
     @userId		NVARCHAR(450) = NULL
    ,@id			uniqueidentifier = NULL
    ,@prod_name		nvarchar(500) = NULL
	,@prod_desc		nvarchar(500) = NULL
	,@prod_line_id	uniqueidentifier = NULL
	,@icon			NVARCHAR(MAX) = NULL
    ,@prod_code		nvarchar(50) = NULL
	,@int_ord		int = 0
	,@app_st		int = 0
	,@prod_category_id  uniqueidentifier = null
AS
BEGIN TRY
	
	SELECT @id [id];
    --2- cac group
    SELECT  *
	FROM [dbo].fn_get_field_group('common_group') 

		SELECT a.[id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			 ,CASE [data_type] 
				  WHEN 'nvarchar' THEN CONVERT(NVARCHAR(350), CASE [field_name] 
                             WHEN 'prod_name' THEN @prod_name
                             WHEN 'prod_desc' THEN @prod_desc
							 when 'prod_code' then @prod_code
							 when 'icon' then @icon
						END) 				  
				 WHEN 'uniqueidentifier' THEN convert(NVARCHAR(350), CASE [field_name] 
							WHEN 'prod_line_id' THEN lower(cast(@prod_line_id AS VARCHAR(50))) 
                            WHEN 'prod_category_id' THEN lower(cast(@prod_category_id AS VARCHAR(50))) 							
					  END)
                ELSE convert(NVARCHAR(50), CASE [field_name] 
					  WHEN 'int_ord' THEN @int_ord
					  WHEN 'app_st' THEN @app_st
					  END)
				END AS columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject] = case when field_name = 'prod_category_id' then [columnObject] + lower(cast(@prod_line_id as varchar(100))) else [columnObject] end
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM 
				(
					SELECT *
					FROM sys_config_form
					WHERE table_name = 'products' 
				)a
		  where 
			(isvisiable = 1 or isRequire = 1)
			
		  order by ordinal

    

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_products_draft' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Userid' + @userId;

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'products',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

