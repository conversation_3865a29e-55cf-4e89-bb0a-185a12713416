
CREATE PROCEDURE [dbo].[utl_errorlog_set] @ErrorCd INT
    , @ErrorMsg VARCHAR(200)
    , @ProcName VARCHAR(50)
    , @TableName VARCHAR(50)
    , @ActionType VARCHAR(3)
    , @SessionID BIGINT
    , @AddlInfo VARCHAR(max)
AS
INSERT INTO utl_Error_Log (
    ErrorNum
    , ErrorMsg
    , ProcName
    , TableName
    , ActionType
    , SessionID
    , AddlInfo
    , CreatedDate
    )
SELECT ErrorNum = @ErrorCd
    , ErrorMsg = @ErrorMsg
    , ProcName = @ProcName
    , TableName = @TableName
    , ActionType = @ActionType
    , SessionID = @SessionID
    , AddlInfo = @AddlInfo
    , CreatedDate = getdate()

GO

