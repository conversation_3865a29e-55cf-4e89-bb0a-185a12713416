CREATE procedure [dbo].[sp_app_account_register_set]
	@phone		nvarchar(20),
	@email		nvarchar(50)=null,
    @password nvarchar(50) = null

as
begin
	declare @valid bit = 1
	declare @mess nvarchar(100) = N'Đăng ký người dùng thành công'
    declare @prefix nvarchar(20) = 'survey_'
    declare @loginName nvarchar(35) = @prefix + @phone
	declare @insertedId uniqueidentifier
	begin try	

		declare @phoneF nvarchar(20)
		declare @cntry_Reg nvarchar(20)
		select @insertedId = id from [User] where phone = @phone

		begin
			if substring(@phone,1,1) = '0' and len(@phone) = 10
			begin			
				set @phoneF = '+84'+ substring(@phone,2,9)
			end
			else if len(@phone) = 9
			begin
				set @phoneF = '+84'+ substring(@phone,1,9)
				set @phone = '0' + @phone
			end
		end


        if Exists(SELECT 1 FROM [User] WHERE  phone = @phone  and is_verify = 1)
		begin
			set @valid = 0
			set @mess = N'<PERSON><PERSON><PERSON> khoản đã tồn tại. Vui lòng quên mật khẩu để đăng nhập.'
			goto FINAL
		end
	
		if not Exists(SELECT 1 FROM [User] WHERE phone = @phone)
			begin
			    set @insertedId = newid()
				INSERT INTO [dbo].[User]
                       ([id]
                       ,[username]
                       --,password
                       ,[fullname]
                       ,[company]
                       ,[position]
                       ,[address]
                       ,[email]
                       ,[phone]
                       ,[is_verify])
                 VALUES
                       (@insertedId
                       ,@loginName
                       --,@password
                       ,null
                       ,null
                       ,null
                       ,null
                       ,null
                       ,@phone
                       ,0)
			end

			select @valid as valid,
			       @mess as messages,
				   @phone as phone
			from [User]
			where id = @insertedId


	end try
	begin catch
		declare	@ErrorNum				int,
				@ErrorMsg				varchar(200),
				@ErrorProc				varchar(50),

				@SessionID				int,
				@AddlInfo				varchar(max)

		set @ErrorNum					= error_number()
		set @ErrorMsg					= 'sp_app_account_register_set ' + error_message()
		set @ErrorProc					= error_procedure()

		set @AddlInfo					= '@UserAuth '  + @phone 
		set @valid = 0
		set @mess = error_message()

		exec utl_ErrorLog_Set @ErrorNum, @ErrorMsg, @ErrorProc, 'User', 'Set', @SessionID, @AddlInfo
	end catch

	FINAL:
	select @valid as valid
		  ,@mess as [messages]


end

GO

