CREATE PROCEDURE [dbo].[sp_product_package_details_fields]
    @userId NVARCHAR(50) = NULL,
    @package_id UNIQUEIDENTIFIER = '39a1e62c-0709-4a60-bf07-2f659c4cf7c0',
    @id UNIQUEIDENTIFIER = null
AS
BEGIN TRY
    DECLARE @package_name nvarchar(100) 
    SELECT @package_name = name from product_package where id = @package_id
    --
    SELECT *
	INTO #product_package_details
	FROM product_package_details
	WHERE id = @id

	IF @ID is not null and not exists(SELECT 1 FROM #product_package_details ) SET @id = null

	select @id gd
		  ,'product_package_details' as tableKey
		  ,groupKey = 'common_group'
	--2- cac group
	SELECT  *
	FROM [dbo].fn_get_field_group('common_group') 
	
	if @id IS NOT NULL
	begin

		SELECT a.[id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,[group_cd]
			 ,CASE [data_type] 
				  WHEN 'nvarchar' THEN CONVERT(NVARCHAR(350), CASE [field_name] 
                             WHEN 'users_limit' THEN b.users_limit
                             WHEN 'description' THEN b.description
						END) 
				  WHEN 'datetime' THEN CASE [field_name] 
							WHEN 'start_dt' THEN FORMAT(b.start_dt,'dd/MM/yyyy') END
                            WHEN 'end_dt' THEN FORMAT(b.end_dt,'dd/MM/yyyy')
                 
				 WHEN 'uniqueidentifier' THEN convert(NVARCHAR(350), CASE [field_name] 
							WHEN 'id' THEN (cast(b.id AS VARCHAR(50))) 
                            WHEN 'prod_id' THEN (cast(b.prod_id AS VARCHAR(50))) 
                            WHEN 'package_id' THEN (cast(b.package_id AS VARCHAR(50))) 
					  END)
                ELSE convert(NVARCHAR(50), CASE [field_name] 
					  WHEN 'package_price_type' THEN b.package_price_type
					  END)
				END AS columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject] 
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM #product_package_details b
			OUTER APPLY
				(
					SELECT *
					FROM sys_config_form
					WHERE table_name = 'product_package_details' 
				)a
		  where 
			(isvisiable = 1 or isRequire = 1)
			
		  order by ordinal
	
	end
	else
	begin
		
		SELECT [id]
			  ,[table_name]
			  ,[field_name]
			  ,[view_type]
			  ,[data_type]
			  ,[ordinal]
			  ,[columnLabel]
			  ,group_cd
			  ,case when a.field_name ='package_id' then cast(@package_id as nvarchar(50))
                    when a.field_name ='package_name' then @package_name  else columnDefault end as columnValue
			  ,[columnClass]
			  ,[columnType]
			  ,[columnObject]
			  ,[isSpecial]
			  ,[isRequire]
			  ,[isDisable]
			  ,[IsVisiable]
			  ,[IsEmpty]
			  ,isnull(a.columnTooltip,a.[columnLabel]) as columnTooltip
			  ,columnDisplay
			  ,isIgnore
		  FROM sys_config_form a
		  where table_name = 'product_package_details' 
		  and (isvisiable = 1 or isRequire = 1)
		  order by ordinal

		--2
	end
END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_product_package_details_field' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'product_package_details',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;

GO

